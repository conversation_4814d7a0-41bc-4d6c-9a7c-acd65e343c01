#!/usr/bin/env python3
import requests
import json
import os

def encrypt_file():
    # Read the file content
    with open('laravel_db_migrate_tool.php', 'r') as f:
        content = f.read()
    
    # Prepare the request data
    data = {
        "files": [
            {
                "filename": "laravel_db_migrate_tool.php",
                "content": content,
                "size": len(content)
            }
        ],
        "encryptionConfig": {
            "level": "advanced",
            "productionSafe": True,
            "antiDebugging": True
        }
    }
    
    # Make the API request
    response = requests.post(
        'http://127.0.0.1:8081/api/encrypt',
        headers={'Content-Type': 'application/json'},
        json=data
    )
    
    if response.status_code == 200:
        result = response.json()
        print("Encryption successful!")
        print(f"Status: {result['status']}")
        
        if 'results' in result and len(result['results']) > 0:
            file_result = result['results'][0]
            print(f"Original size: {file_result['original_size']} bytes")
            print(f"Encrypted size: {file_result['encrypted_size']} bytes")
            print(f"Protection level: {file_result['protection_level']}%")
            print(f"Download URL: {file_result['download_url']}")
            
            # Download the encrypted file
            download_response = requests.get(f"http://127.0.0.1:8081{file_result['download_url']}")
            if download_response.status_code == 200:
                with open('custom_encrypted_laravel_db_migrate_tool.php', 'wb') as f:
                    f.write(download_response.content)
                print("Encrypted file saved as: custom_encrypted_laravel_db_migrate_tool.php")
            else:
                print(f"Failed to download encrypted file: {download_response.status_code}")
    else:
        print(f"Encryption failed: {response.status_code}")
        print(response.text)

if __name__ == "__main__":
    encrypt_file()
