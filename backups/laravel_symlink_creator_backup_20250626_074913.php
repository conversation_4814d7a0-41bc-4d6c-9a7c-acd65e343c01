<?php
// Recursive directory removal function
function removeDirectoryRecursively($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    try {
        $files = array_diff(scandir($dir), array('.', '..'));

        foreach ($files as $file) {
            $filePath = $dir . DIRECTORY_SEPARATOR . $file;

            if (is_dir($filePath)) {
                // Recursively remove subdirectory
                if (!removeDirectoryRecursively($filePath)) {
                    return false;
                }
            } else {
                // Remove file
                if (!unlink($filePath)) {
                    return false;
                }
            }
        }

        // Remove the directory itself
        return rmdir($dir);
    } catch (Exception $e) {
        return false;
    }
}

// PHP Logic Section
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    // Check if this is a test storage request
    if (isset($_POST['test_storage']) && $_POST['test_storage'] === 'true') {
        try {
            $projectRoot = dirname(dirname(__DIR__));
            $storagePath = $projectRoot . '/storage/app/public';
            $publicStoragePath = dirname(__DIR__) . '/storage';
            $testImagePath = $storagePath . '/test_placeholder.png';
            $publicTestPath = '/storage/test_placeholder.png';

            $diagnostics = [];
            $result = ['success' => false, 'diagnostics' => [], 'testImagePath' => $publicTestPath];

            // Check if symlink exists
            if (is_link($publicStoragePath)) {
                $diagnostics[] = '✓ Symlink exists at: ' . $publicStoragePath;
                $diagnostics[] = '✓ Symlink target: ' . readlink($publicStoragePath);
            } else {
                $diagnostics[] = '❌ Symlink does not exist at: ' . $publicStoragePath;
                throw new Exception('Symlink not found');
            }

            // Check if test image exists
            if (file_exists($testImagePath)) {
                $diagnostics[] = '✓ Test image exists at: ' . $testImagePath;
                $perms = substr(sprintf('%o', fileperms($testImagePath)), -3);
                $diagnostics[] = '✓ Test image permissions: ' . $perms;

                // Fix permissions if needed
                if ($perms !== '644') {
                    if (chmod($testImagePath, 0644)) {
                        $diagnostics[] = '✓ Fixed test image permissions to 644';
                    } else {
                        $diagnostics[] = '⚠ Failed to fix test image permissions';
                    }
                }
            } else {
                $diagnostics[] = '❌ Test image not found, creating new one...';

                // Create test image
                $testImage = imagecreatetruecolor(200, 100);
                $backgroundColor = imagecolorallocate($testImage, 45, 55, 72);
                $textColor = imagecolorallocate($testImage, 255, 255, 255);
                imagefill($testImage, 0, 0, $backgroundColor);
                imagestring($testImage, 5, 50, 40, 'Test Image', $textColor);

                if (imagepng($testImage, $testImagePath)) {
                    chmod($testImagePath, 0644);
                    $diagnostics[] = '✓ Created new test image with 644 permissions';
                } else {
                    throw new Exception('Failed to create test image');
                }
                imagedestroy($testImage);
            }

            // Check symlink permissions
            $symlinkPerms = substr(sprintf('%o', fileperms($publicStoragePath)), -3);
            $diagnostics[] = '✓ Symlink permissions: ' . $symlinkPerms;

            // Check and fix .htaccess for public/storage
            $htaccessPath = $publicStoragePath . '/.htaccess';
            $needsHtaccessUpdate = false;

            if (file_exists($htaccessPath)) {
                $currentHtaccess = file_get_contents($htaccessPath);
                // Check if the current .htaccess is too restrictive (denies all files)
                if (strpos($currentHtaccess, 'Deny from all') !== false && strpos($currentHtaccess, '<FilesMatch ".*">') !== false) {
                    $diagnostics[] = '⚠ Found restrictive .htaccess that denies all file access';
                    $needsHtaccessUpdate = true;
                } else {
                    $diagnostics[] = '✓ .htaccess file exists and appears compatible';
                }
            } else {
                $diagnostics[] = '❌ No .htaccess file found in storage directory';
                $needsHtaccessUpdate = true;
            }

            if ($needsHtaccessUpdate) {
                $htaccessContent = "# Laravel Storage Directory Access Rules
# Allow access to common file types while maintaining security

# Allow common file types
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp|svg|pdf|doc|docx|txt|zip|mp4|mp3)$\">
    <IfModule mod_authz_core.c>
        Require all granted
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order Allow,Deny
        Allow from all
    </IfModule>
</FilesMatch>

# Prevent execution of PHP files for security
<FilesMatch \"\\.(php|phtml|php3|php4|php5|php7|phps)$\">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order Allow,Deny
        Deny from all
    </IfModule>
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable server signature
ServerSignature Off";

                if (file_put_contents($htaccessPath, $htaccessContent)) {
                    $diagnostics[] = '✅ Updated .htaccess file with proper Laravel storage rules';
                } else {
                    $diagnostics[] = '❌ Failed to update .htaccess file';
                }
            }

            $result['success'] = true;
            $result['diagnostics'] = $diagnostics;

            echo json_encode($result);
            exit;
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage(), 'diagnostics' => $diagnostics ?? []]);
            exit;
        }
    }

    // Check if this is a cleanup request
    if (isset($_POST['cleanup']) && $_POST['cleanup'] === 'true') {
        try {
            $projectRoot = dirname(dirname(__DIR__));
            $storagePath = $projectRoot . '/storage/app/public';
            $testImagePath = $storagePath . '/test_placeholder.png';
            
            $result = ['success' => true, 'message' => ''];
            
            if (file_exists($testImagePath)) {
                if (unlink($testImagePath)) {
                    $result['message'] = 'Test image successfully deleted';
                } else {
                    throw new Exception('Failed to delete test image');
                }
            } else {
                $result['message'] = 'Test image already removed';
            }
            
            echo json_encode($result);
            exit;
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    try {
        // Get the project root directory by going up one level from public
        $projectRoot = dirname(dirname(__DIR__));
        $publicPath = $projectRoot . '/public';
        $storagePath = $projectRoot . '/storage/app/public';
        $publicStoragePath = $publicPath . '/storage';

        $results = [
            'success' => true,
            'messages' => [],
            'projectRoot' => $projectRoot,
            'target' => $storagePath,
            'link' => $publicStoragePath,
            'testImagePath' => ''
        ];

        // Check if storage/app/public exists
        if (!is_dir($storagePath)) {
            mkdir($storagePath, 0755, true);
            $results['messages'][] = [
                'type' => 'info',
                'message' => "Created directory: {$storagePath}"
            ];
        }

        // Remove existing symlink/file/directory if it exists
        if (file_exists($publicStoragePath) || is_link($publicStoragePath)) {
            if (is_link($publicStoragePath)) {
                if (unlink($publicStoragePath)) {
                    $results['messages'][] = [
                        'type' => 'warning',
                        'message' => "Removed existing symlink at: {$publicStoragePath}"
                    ];
                } else {
                    throw new Exception("Failed to remove existing symlink at: {$publicStoragePath}");
                }
            } elseif (is_dir($publicStoragePath)) {
                // Get directory info before removal
                $dirContents = [];
                try {
                    $files = array_diff(scandir($publicStoragePath), array('.', '..'));
                    $dirContents = array_slice($files, 0, 5); // Show first 5 items
                    if (count($files) > 5) {
                        $dirContents[] = '... and ' . (count($files) - 5) . ' more items';
                    }
                } catch (Exception $e) {
                    $dirContents = ['Unable to scan directory contents'];
                }

                $results['messages'][] = [
                    'type' => 'info',
                    'message' => "Found existing directory with contents: " . implode(', ', $dirContents)
                ];

                // Remove directory recursively
                if (removeDirectoryRecursively($publicStoragePath)) {
                    $results['messages'][] = [
                        'type' => 'warning',
                        'message' => "Successfully removed existing directory (with all contents) at: {$publicStoragePath}"
                    ];
                } else {
                    // Try using shell command as fallback
                    $escapedPath = escapeshellarg($publicStoragePath);
                    $output = [];
                    $returnCode = 0;

                    // Try rm -rf as fallback
                    exec("rm -rf {$escapedPath} 2>&1", $output, $returnCode);

                    if ($returnCode === 0 && !file_exists($publicStoragePath)) {
                        $results['messages'][] = [
                            'type' => 'warning',
                            'message' => "Successfully removed directory using system command (fallback method)"
                        ];
                    } else {
                        // Get more detailed error information
                        $errorDetails = [];
                        $errorDetails[] = "Directory exists: " . (is_dir($publicStoragePath) ? 'Yes' : 'No');
                        $errorDetails[] = "Directory readable: " . (is_readable($publicStoragePath) ? 'Yes' : 'No');
                        $errorDetails[] = "Directory writable: " . (is_writable($publicStoragePath) ? 'Yes' : 'No');
                        $errorDetails[] = "Parent directory writable: " . (is_writable(dirname($publicStoragePath)) ? 'Yes' : 'No');
                        $errorDetails[] = "Shell command output: " . implode(' ', $output);
                        $errorDetails[] = "Shell command return code: " . $returnCode;

                        throw new Exception("Failed to remove existing directory at: {$publicStoragePath}\n\nDetails:\n" . implode("\n", $errorDetails));
                    }
                }
            } elseif (is_file($publicStoragePath)) {
                if (unlink($publicStoragePath)) {
                    $results['messages'][] = [
                        'type' => 'warning',
                        'message' => "Removed existing file at: {$publicStoragePath}"
                    ];
                } else {
                    throw new Exception("Failed to remove existing file at: {$publicStoragePath}");
                }
            }
        }

        // Final check before creating symlink
        if (file_exists($publicStoragePath) || is_link($publicStoragePath)) {
            throw new Exception("Target path still exists after cleanup attempt: {$publicStoragePath}");
        }

        // Create the symlink with detailed error reporting
        $symlinkResult = @symlink($storagePath, $publicStoragePath);
        $lastError = error_get_last();

        if ($symlinkResult) {
            $results['messages'][] = [
                'type' => 'success',
                'message' => "Successfully created symbolic link!"
            ];

            // Verify the symlink was created correctly
            if (is_link($publicStoragePath)) {
                $linkTarget = readlink($publicStoragePath);
                $realTarget = realpath($linkTarget);
                $results['messages'][] = [
                    'type' => 'info',
                    'message' => "Symlink verification: Points to {$linkTarget}"
                ];
                if ($realTarget) {
                    $results['messages'][] = [
                        'type' => 'info',
                        'message' => "Real path resolved to: {$realTarget}"
                    ];
                }
            } else {
                $results['messages'][] = [
                    'type' => 'warning',
                    'message' => "Warning: Symlink creation reported success but verification failed"
                ];
            }

            // Test placeholder image
            $placeholderSrc = $publicPath . '/scripts/placeholder.png';
            $placeholderDest = $storagePath . '/test_placeholder.png';
            
            // Check if source placeholder exists
            if (file_exists($placeholderSrc)) {
                if (copy($placeholderSrc, $placeholderDest)) {
                    $results['messages'][] = [
                        'type' => 'success',
                        'message' => "Successfully copied test placeholder image from: {$placeholderSrc}"
                    ];
                    $results['testImagePath'] = '/storage/test_placeholder.png';
                } else {
                    $results['messages'][] = [
                        'type' => 'warning',
                        'message' => "Failed to copy test placeholder image from: {$placeholderSrc}"
                    ];
                }
            } else {
                $results['messages'][] = [
                    'type' => 'info',
                    'message' => "Placeholder image not found at: {$placeholderSrc}, creating a test image instead"
                ];
                // Create a test image if placeholder doesn't exist
                $testImage = imagecreatetruecolor(200, 100);
                $backgroundColor = imagecolorallocate($testImage, 45, 55, 72); // Dark background
                $textColor = imagecolorallocate($testImage, 255, 255, 255); // White text
                imagefill($testImage, 0, 0, $backgroundColor);

                // Add text to the image
                $text = 'Test Image';
                $font = 5; // Built-in font
                $textWidth = imagefontwidth($font) * strlen($text);
                $textHeight = imagefontheight($font);
                $x = (200 - $textWidth) / 2;
                $y = (100 - $textHeight) / 2;
                imagestring($testImage, $font, $x, $y, $text, $textColor);

                if (imagepng($testImage, $placeholderDest)) {
                    $results['messages'][] = [
                        'type' => 'success',
                        'message' => "Created test PNG image at: {$placeholderDest}"
                    ];
                    $results['testImagePath'] = '/storage/test_placeholder.png';
                } else {
                    $results['messages'][] = [
                        'type' => 'warning',
                        'message' => "Failed to create test PNG image"
                    ];
                }
                imagedestroy($testImage);
            }

            // Set proper permissions
            chmod($publicStoragePath, 0755);
            if (file_exists($placeholderDest)) {
                chmod($placeholderDest, 0644);
            }

            // Create proper .htaccess file for Laravel storage access
            $htaccessPath = $publicStoragePath . '/.htaccess';
            $htaccessContent = "# Laravel Storage Directory Access Rules
# Allow access to common file types while maintaining security

# Allow common file types
<FilesMatch \"\\.(jpg|jpeg|png|gif|webp|svg|pdf|doc|docx|txt|zip|mp4|mp3)$\">
    <IfModule mod_authz_core.c>
        Require all granted
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order Allow,Deny
        Allow from all
    </IfModule>
</FilesMatch>

# Prevent execution of PHP files for security
<FilesMatch \"\\.(php|phtml|php3|php4|php5|php7|phps)$\">
    <IfModule mod_authz_core.c>
        Require all denied
    </IfModule>
    <IfModule !mod_authz_core.c>
        Order Allow,Deny
        Deny from all
    </IfModule>
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable server signature
ServerSignature Off";

            if (file_put_contents($htaccessPath, $htaccessContent)) {
                $results['messages'][] = [
                    'type' => 'success',
                    'message' => "Created proper .htaccess file with Laravel storage access rules"
                ];
            } else {
                $results['messages'][] = [
                    'type' => 'warning',
                    'message' => "Failed to create .htaccess file - you may need to create it manually"
                ];
            }
        } else {
            // Detailed error reporting for symlink failure
            $errorMsg = "Failed to create symbolic link";
            if ($lastError && isset($lastError['message'])) {
                $errorMsg .= ": " . $lastError['message'];
            }

            // Add diagnostic information
            $diagnostics = [];
            $diagnostics[] = "Source exists: " . (is_dir($storagePath) ? 'Yes' : 'No');
            $diagnostics[] = "Target parent writable: " . (is_writable(dirname($publicStoragePath)) ? 'Yes' : 'No');
            $diagnostics[] = "Current user: " . (function_exists('posix_getpwuid') && function_exists('posix_geteuid') ? posix_getpwuid(posix_geteuid())['name'] : 'Unknown');
            $diagnostics[] = "Storage path permissions: " . substr(sprintf('%o', fileperms($storagePath)), -4);
            $diagnostics[] = "Public path permissions: " . substr(sprintf('%o', fileperms(dirname($publicStoragePath))), -4);

            $errorMsg .= "\n\nDiagnostics:\n" . implode("\n", $diagnostics);

            throw new Exception($errorMsg);
        }

        echo json_encode($results);
        exit;
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Storage Symlink Creator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #ff2d20 0%, #ff6b35 100%);
            padding: 30px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.05) 10px,
                rgba(255, 255, 255, 0.05) 20px
            );
            animation: shine 20s linear infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo {
            width: 300px;
            height: auto;
            margin: 0 auto 20px;
            filter: brightness(0) invert(1);
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .status-section {
            margin-bottom: 30px;
        }

        .status-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid #28a745;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }

        .status-icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }

        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }

        .output-section {
            background: #1e1e1e;
            color: #e6e6e6;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .output-section::-webkit-scrollbar {
            width: 8px;
        }

        .output-section::-webkit-scrollbar-track {
            background: #2a2a2a;
            border-radius: 4px;
        }

        .output-section::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        .output-section::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 150px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff2d20 0%, #ff6b35 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #e02417 0%, #e55a2e 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 45, 32, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1fa682 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            transition: border-color 0.3s ease;
        }

        .info-item:hover {
            border-color: #ff2d20;
        }

        .info-label {
            font-weight: 600;
            color: #6c757d;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            word-break: break-all;
            border-left: 3px solid #ff2d20;
        }

        .warning-banner {
            background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 600;
            text-align: center;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                min-width: 200px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 934 345" style="enable-background:new 0 0 934 345;" xml:space="preserve">
<g>
	<g>
		<polygon style="fill:#EF3B2D;" points="389.647,121.376 373.181,121.376 373.181,221.606 420.575,221.606 420.575,206.858     389.647,206.858   "/>
		<path style="fill:#EF3B2D;" d="M478.706,163.902c-2.101-3.341-5.083-5.965-8.949-7.875c-3.865-1.909-7.756-2.864-11.669-2.864    c-5.062,0-9.69,0.931-13.89,2.792c-4.201,1.861-7.804,4.417-10.811,7.661c-3.007,3.246-5.347,6.993-7.016,11.239    c-1.672,4.249-2.506,8.713-2.506,13.389c0,4.774,0.834,9.26,2.506,13.459c1.669,4.202,4.009,7.925,7.016,11.169    c3.007,3.246,6.609,5.799,10.811,7.66c4.199,1.861,8.828,2.792,13.89,2.792c3.913,0,7.804-0.955,11.669-2.863    c3.866-1.908,6.849-4.533,8.949-7.875v9.021h15.607v-66.725h-15.607V163.902z M477.275,196.405    c-0.955,2.578-2.291,4.821-4.009,6.73c-1.719,1.91-3.795,3.437-6.229,4.582c-2.435,1.146-5.133,1.718-8.091,1.718    c-2.96,0-5.633-0.572-8.019-1.718c-2.387-1.146-4.438-2.672-6.156-4.582c-1.719-1.909-3.032-4.152-3.938-6.73    c-0.909-2.577-1.36-5.298-1.36-8.161c0-2.864,0.451-5.585,1.36-8.162c0.905-2.577,2.219-4.819,3.938-6.729    c1.718-1.908,3.77-3.437,6.156-4.582c2.386-1.146,5.059-1.718,8.019-1.718c2.958,0,5.656,0.572,8.091,1.718    c2.434,1.146,4.51,2.674,6.229,4.582c1.718,1.91,3.054,4.152,4.009,6.729c0.953,2.577,1.432,5.298,1.432,8.162    C478.706,191.107,478.228,193.828,477.275,196.405z"/>
		<path style="fill:#EF3B2D;" d="M605.569,163.902c-2.101-3.341-5.083-5.965-8.949-7.875c-3.865-1.909-7.756-2.864-11.669-2.864    c-5.062,0-9.69,0.931-13.89,2.792c-4.201,1.861-7.804,4.417-10.811,7.661c-3.007,3.246-5.347,6.993-7.016,11.239    c-1.672,4.249-2.506,8.713-2.506,13.389c0,4.774,0.834,9.26,2.506,13.459c1.669,4.202,4.009,7.925,7.016,11.169    c3.007,3.246,6.609,5.799,10.811,7.66c4.199,1.861,8.828,2.792,13.89,2.792c3.913,0,7.804-0.955,11.669-2.863    c3.866-1.908,6.849-4.533,8.949-7.875v9.021h15.607v-66.725h-15.607V163.902z M604.137,196.405    c-0.955,2.578-2.291,4.821-4.009,6.73c-1.719,1.91-3.795,3.437-6.229,4.582c-2.435,1.146-5.133,1.718-8.091,1.718    c-2.96,0-5.633-0.572-8.019-1.718c-2.387-1.146-4.438-2.672-6.156-4.582c-1.719-1.909-3.032-4.152-3.938-6.73    c-0.909-2.577-1.36-5.298-1.36-8.161c0-2.864,0.451-5.585,1.36-8.162c0.905-2.577,2.219-4.819,3.938-6.729    c1.718-1.908,3.77-3.437,6.156-4.582c2.386-1.146,5.059-1.718,8.019-1.718c2.958,0,5.656,0.572,8.091,1.718    c2.434,1.146,4.51,2.674,6.229,4.582c1.718,1.91,3.054,4.152,4.009,6.729c0.953,2.577,1.432,5.298,1.432,8.162    C605.569,191.107,605.09,193.828,604.137,196.405z"/>
		<rect x="776.781" y="121.376" style="fill:#EF3B2D;" width="15.606" height="100.23"/>
		<polygon style="fill:#EF3B2D;" points="506.628,221.606 522.235,221.606 522.235,170.238 549.011,170.238 549.011,154.882     506.628,154.882   "/>
		<polygon style="fill:#EF3B2D;" points="683.748,154.882 664.132,205.978 644.516,154.882 628.708,154.882 654.325,221.606     673.939,221.606 699.556,154.882   "/>
		<path style="fill:#EF3B2D;" d="M733.595,153.166c-19.112,0-34.239,15.706-34.239,35.079c0,21.416,14.641,35.079,36.239,35.079    c12.088,0,19.806-4.622,29.234-14.688l-10.544-8.158c-0.006,0.008-7.958,10.449-19.832,10.449    c-13.802,0-19.612-11.127-19.612-16.884h51.777C769.338,172,754.846,153.166,733.595,153.166z M714.882,182.446    c0.12-1.284,1.917-16.884,18.589-16.884c16.671,0,18.697,15.598,18.813,16.884H714.882z"/>
	</g>
	<path style="fill:#EF3B2D;" d="M325.683,120.592c-0.024-0.088-0.073-0.165-0.104-0.25c-0.058-0.157-0.108-0.316-0.191-0.46   c-0.056-0.097-0.137-0.176-0.203-0.265c-0.087-0.117-0.161-0.242-0.265-0.345c-0.085-0.086-0.194-0.148-0.29-0.223   c-0.109-0.085-0.206-0.182-0.327-0.252c0,0-0.001,0-0.002-0.001c-0.001,0-0.001-0.001-0.002-0.002L288.651,98.27   c-0.917-0.528-2.047-0.528-2.964,0l-35.647,20.522c-0.001,0-0.001,0.001-0.002,0.002c-0.001,0-0.002,0-0.002,0.001   c-0.121,0.07-0.219,0.167-0.327,0.252c-0.096,0.075-0.205,0.138-0.29,0.223c-0.103,0.103-0.178,0.228-0.265,0.345   c-0.066,0.089-0.147,0.169-0.203,0.265c-0.083,0.144-0.133,0.304-0.191,0.46c-0.031,0.085-0.08,0.162-0.104,0.25   c-0.067,0.249-0.103,0.51-0.103,0.776v38.979l-29.706,17.103v-76.255c0-0.265-0.036-0.526-0.103-0.776   c-0.024-0.088-0.073-0.165-0.104-0.25c-0.058-0.157-0.108-0.316-0.191-0.46c-0.056-0.097-0.137-0.176-0.203-0.265   c-0.087-0.117-0.161-0.242-0.265-0.345c-0.085-0.086-0.194-0.148-0.29-0.223c-0.109-0.085-0.206-0.182-0.327-0.252   c0,0-0.001,0-0.002-0.001c-0.001,0-0.001-0.001-0.002-0.002l-35.647-20.523c-0.917-0.528-2.047-0.528-2.964,0l-35.647,20.523   c-0.001,0-0.001,0.001-0.002,0.002c-0.001,0-0.002,0-0.002,0.001c-0.121,0.07-0.219,0.167-0.327,0.252   c-0.096,0.075-0.205,0.138-0.29,0.223c-0.103,0.103-0.178,0.228-0.265,0.345c-0.066,0.089-0.147,0.169-0.203,0.265   c-0.083,0.144-0.133,0.304-0.191,0.46c-0.031,0.085-0.08,0.162-0.104,0.25c-0.067,0.249-0.103,0.51-0.103,0.776v122.09   c0,1.063,0.568,2.044,1.489,2.575l71.293,41.045c0.156,0.089,0.324,0.143,0.49,0.202c0.078,0.028,0.15,0.074,0.23,0.095   c0.249,0.066,0.506,0.1,0.762,0.1c0.256,0,0.512-0.033,0.762-0.1c0.069-0.018,0.132-0.059,0.2-0.083   c0.176-0.061,0.354-0.119,0.519-0.214l71.293-41.045c0.921-0.53,1.489-1.512,1.489-2.575v-38.979l34.158-19.666   c0.921-0.53,1.489-1.512,1.489-2.575v-40.697C325.786,121.102,325.751,120.841,325.683,120.592z M215.87,219.867l-29.648-16.779   l31.136-17.926c0,0,0,0,0.001-0.001l34.164-19.669l29.674,17.084l-21.772,12.428L215.87,219.867z M284.199,143.608v33.841   l-12.475-7.182l-17.231-9.92v-33.841l12.475,7.182L284.199,143.608z M287.169,104.273l29.693,17.095l-29.693,17.095l-29.693-17.095   L287.169,104.273z M195.675,190.789l-12.475,7.182v-74.538l17.231-9.92l12.475-7.182v74.537L195.675,190.789z M180.229,84.098   l29.693,17.095l-29.693,17.095l-29.693-17.095L180.229,84.098z M147.553,106.332l12.475,7.182l17.231,9.92v79.676   c0,0.002,0.001,0.003,0.001,0.005s-0.001,0.004-0.001,0.006c0,0.114,0.032,0.221,0.045,0.333c0.017,0.146,0.021,0.294,0.059,0.434   c0.001,0.002,0.001,0.005,0.002,0.007c0.032,0.117,0.094,0.222,0.14,0.334c0.051,0.124,0.088,0.255,0.156,0.371   c0.001,0.003,0.002,0.006,0.004,0.009c0.061,0.105,0.149,0.191,0.222,0.288c0.081,0.105,0.149,0.22,0.244,0.314   c0.003,0.003,0.005,0.007,0.008,0.01c0.084,0.083,0.19,0.142,0.284,0.215c0.106,0.083,0.202,0.178,0.32,0.247   c0.004,0.002,0.009,0.003,0.013,0.005c0.004,0.002,0.007,0.006,0.011,0.008l34.139,19.321v34.175l-65.352-37.625V106.332z    M284.199,221.567l-65.352,37.625v-34.182l48.399-27.628l16.953-9.677V221.567z M319.845,160.347l-29.706,17.102v-33.841   l17.231-9.92l12.475-7.182V160.347z"/>
</g>
</svg>
            </div>
            <h1>Laravel Storage Symlink Creator</h1>
            <p>Automatically create symbolic links for Laravel storage</p>
        </div>

        <div class="content">
            <div id="results">
                <!-- Results will be displayed here -->
            </div>

            <div class="actions">
                <button class="btn btn-primary" onclick="createSymlink()" id="createBtn">
                    <span>🔗</span> Create Symlink
                </button>
                <a href="/" class="btn btn-secondary">
                    <span>🏠</span> Return to Home
                </a>
                <button class="btn btn-success" onclick="testStorage()" id="testBtn">
                    <span>🧪</span> Test Storage Access
                </button>
            </div>

            <div class="warning-banner">
                ⚠️ <strong>Security Notice:</strong> Remember to delete this script after use!
            </div>

            <!-- Creator Details Section -->
            <div style="
                background: #2d3748;
                color: white;
                border-radius: 12px;
                padding: 25px;
                margin-top: 30px;
                text-align: center;
            ">
                <h3 style="margin-bottom: 20px; font-size: 1.25rem; color: white;">
                    👨‍💻 Created By
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; text-align: left;">
                    <div>
                        <strong style="color: #ff2d20;">Firoz Anam</strong><br>
                        <small style="color: #cbd5e0;">Full Stack Developer</small>
                    </div>
                    <div>
                        <strong style="color: #ff2d20;">📧 Email:</strong><br>
                        <a href="mailto:<EMAIL>" style="color: #cbd5e0; text-decoration: none;">
                            <EMAIL>
                        </a>
                    </div>
                    <div>
                        <strong style="color: #ff2d20;">🐙 GitHub:</strong><br>
                        <a href="https://github.com/firozanam" target="_blank" style="color: #cbd5e0; text-decoration: none;">
                            github.com/firozanam
                        </a>
                    </div>
                    <div>
                        <strong style="color: #ff2d20;">💼 LinkedIn:</strong><br>
                        <a href="https://www.linkedin.com/in/firozanam" target="_blank" style="color: #cbd5e0; text-decoration: none;">
                            linkedin.com/in/firozanam
                        </a>
                    </div>
                    <div>
                        <strong style="color: #ff2d20;">📱 WhatsApp:</strong><br>
                        <a href="https://wa.me/8801788544788" target="_blank" style="color: #cbd5e0; text-decoration: none;">
                            +8801788544788
                        </a>
                    </div>
                    <div>
                        <strong style="color: #ff2d20;">🌐 Website:</strong><br>
                        <a href="https://neurotechsystem.com" target="_blank" style="color: #cbd5e0; text-decoration: none;">
                            neurotechsystem.com
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createSymlink() {
            const createBtn = document.getElementById('createBtn');
            const resultsDiv = document.getElementById('results');
            const originalText = createBtn.innerHTML;
            
            createBtn.innerHTML = '<div class="loading"></div> Creating...';
            createBtn.disabled = true;

            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                let resultHTML = '<div class="status-section">';

                // Check if there's an error in the response
                if (!data.success && data.error) {
                    resultHTML += `
                        <div class="status-card error">
                            <span class="status-icon">❌</span>
                            <strong>Error:</strong> ${data.error}
                        </div>
                    `;
                    resultHTML += '</div>';
                    resultsDiv.innerHTML = resultHTML;
                    return;
                }

                // Project root info
                if (data.projectRoot) {
                    resultHTML += `
                        <div class="status-card">
                            <span class="status-icon success">✅</span>
                            <strong>Project Root determined as:</strong> ${data.projectRoot}
                        </div>
                    `;
                }

                // Path information
                if (data.target && data.link) {
                    resultHTML += `
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Target (actual files location)</div>
                                <div class="info-value">${data.target}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Link Name (publicly accessible path)</div>
                                <div class="info-value">${data.link}</div>
                            </div>
                        </div>
                    `;
                }

                // Messages - Check if messages array exists and is not empty
                if (data.messages && Array.isArray(data.messages) && data.messages.length > 0) {
                    data.messages.forEach(msg => {
                        let icon = msg.type === 'success' ? '✅' :
                                 msg.type === 'warning' ? '⚠️' : 'ℹ️';

                        resultHTML += `
                            <div class="status-card ${msg.type}">
                                <span class="status-icon">${icon}</span>
                                ${msg.message}
                            </div>
                        `;
                    });
                }

                if (data.testImagePath) {
                    resultHTML += `
                        <div class="status-card success">
                            <span class="status-icon">✅</span>
                            Test image created at: ${data.testImagePath}
                            <br>
                            <img src="${data.testImagePath}" alt="Test Image" style="max-width: 100px; margin-top: 10px;">
                        </div>
                    `;
                }

                resultHTML += '</div>';

                // Add console output section - Check if messages exist
                resultHTML += `
                    <div class="output-section">
                        <div style="color: #98c379; margin-bottom: 10px;">--- Laravel Symlink Creation Output ---</div>
                `;

                if (data.messages && Array.isArray(data.messages) && data.messages.length > 0) {
                    resultHTML += data.messages.map(msg => `
                        <div style="color: ${
                            msg.type === 'success' ? '#98c379' :
                            msg.type === 'warning' ? '#ffc107' : '#61dafb'
                        };">${msg.message}</div>
                    `).join('');
                }

                resultHTML += `
                        <div style="color: #e06c75; margin-top: 15px;">--- Script finished ---</div>
                        <div style="color: #ffc107; font-weight: bold;">IMPORTANT: Remember to DELETE THIS SCRIPT from your server immediately!</div>
                        <div style="color: #ffc107;">Path: ${window.location.pathname}</div>
                    </div>
                `;

                resultsDiv.innerHTML = resultHTML;

                // Store the test image path for the test function
                if (data.testImagePath) {
                    window.testImagePath = data.testImagePath;
                }
            })
            .catch(error => {
                console.error('Symlink creation error:', error);
                resultsDiv.innerHTML = `
                    <div class="status-section">
                        <div class="status-card error">
                            <span class="status-icon">❌</span>
                            <strong>Network/Request Error:</strong> ${error.message}
                        </div>
                        <div class="output-section">
                            <div style="color: #e06c75; margin-bottom: 10px;">--- Error Details ---</div>
                            <div style="color: #ffc107;">Error Type: ${error.name || 'Unknown'}</div>
                            <div style="color: #ffc107;">Error Message: ${error.message}</div>
                            <div style="color: #61dafb; margin-top: 10px;">Please check:</div>
                            <div style="color: #61dafb;">• Server permissions</div>
                            <div style="color: #61dafb;">• PHP error logs</div>
                            <div style="color: #61dafb;">• Network connectivity</div>
                        </div>
                    </div>
                `;
            })
            .finally(() => {
                createBtn.innerHTML = originalText;
                createBtn.disabled = false;
            });
        }

        function testStorage() {
            const testBtn = document.getElementById('testBtn');
            const originalText = testBtn.innerHTML;

            testBtn.innerHTML = '<div class="loading"></div> Testing...';
            testBtn.disabled = true;

            // First, run server-side diagnostics
            const formData = new FormData();
            formData.append('test_storage', 'true');

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Now test the actual HTTP access
                    const testImagePath = data.testImagePath || '/storage/test_placeholder.png';

                    fetch(testImagePath + '?t=' + new Date().getTime())
                    .then(response => {
                        if (response.ok) {
                            alert('✅ Storage test successful! The test image is accessible at: ' + testImagePath);
                            // After successful test, clean up the test image
                            cleanupTestImage();
                        } else {
                            throw new Error('HTTP access failed. Status: ' + response.status);
                        }
                    })
                    .catch(error => {
                        alert('❌ HTTP Access Test Failed: ' + error.message + '\n\nDiagnostics:\n' + data.diagnostics.join('\n') + '\n\nPlease check your web server configuration.');
                    });
                } else {
                    alert('❌ Server-side test failed: ' + data.error + '\n\nDiagnostics:\n' + (data.diagnostics ? data.diagnostics.join('\n') : 'No diagnostics available'));
                }
            })
            .catch(error => {
                alert('❌ Test request failed: ' + error.message);
            })
            .finally(() => {
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            });
        }

        function cleanupTestImage() {
            const formData = new FormData();
            formData.append('cleanup', 'true');

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data && data.success) {
                    // Update the UI to show the image was deleted
                    const testImageCard = document.querySelector('.status-card.success img');
                    if (testImageCard) {
                        const card = testImageCard.closest('.status-card');
                        if (card) {
                            card.innerHTML = `
                                <span class="status-icon">✅</span>
                                Test completed and test image removed successfully
                            `;
                        }
                    }

                    // Add cleanup message to the output section
                    const outputSection = document.querySelector('.output-section');
                    if (outputSection) {
                        const cleanupDiv = document.createElement('div');
                        cleanupDiv.style.color = '#98c379';
                        cleanupDiv.textContent = '✨ Cleanup: ' + (data.message || 'Test image removed');
                        outputSection.insertBefore(cleanupDiv, outputSection.lastElementChild);
                    }
                } else {
                    console.error('Failed to cleanup test image:', data ? data.error : 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error during cleanup:', error);
            });
        }
    </script>
</body>
</html>