<?php
/**
 * Laravel Database Migration Tool for cPanel Shared Hosting
 *
 * This script allows you to run database migrations on shared hosting
 * where terminal access is limited.
 *
 * ⚠️ SECURITY WARNING: This script is potentially dangerous as it allows
 * direct database modifications. Use it only temporarily and delete immediately after use.
 *
 * Usage: Upload this script to your public/scripts directory and access it via browser.
 */

// Set execution time limit to 300 seconds (5 minutes)
set_time_limit(300);

// Start output buffering
ob_start();

// Define the base directory (Laravel root)
$baseDir = dirname(dirname(__DIR__));

// Require the autoloader
require $baseDir . '/vendor/autoload.php';

// Load the environment file
$dotenv = Dotenv\Dotenv::createImmutable($baseDir);
$dotenv->load();

// Bootstrap Laravel
$app = require_once $baseDir . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Database Migration Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .badge-success { background-color: #28a745; color: white; }
        .badge-pending { background-color: #ffc107; color: #212529; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        input[type="checkbox"] { margin-right: 8px; }
        button { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background-color: #0056b3; }
        .danger { background-color: #dc3545; }
        .danger:hover { background-color: #c82333; }
        h1, h2, h3 { color: #333; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #e9ecef; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning">
            <strong>⚠️ SECURITY WARNING:</strong> This script can modify your database schema. Delete this file immediately after use!
        </div>
        
        <h1>Laravel Database Migration Tool</h1>
        <p>===============================</p>
';

// Verify database connection
try {
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo '<div class="success">
        <strong>Database connection successful:</strong> ' . htmlspecialchars(\Illuminate\Support\Facades\DB::connection()->getDatabaseName()) . '
    </div>';
} catch (\Exception $e) {
    echo '<div class="error">
        <h3>Database Connection Error</h3>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
        <p>Please check your database credentials in the .env file.</p>
    </div>';
    exit;
}

// Check if we should run migrations
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    // Handle migration actions
    if ($action === 'migrate') {
        echo '<div class="section">
            <h3>Running Migrations</h3>
            <pre>';
        
        try {
            // Capture output
            ob_start();
            
            // Run the migration
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $step = isset($_POST['step']) && $_POST['step'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate', [
                '--force' => $force,
                '--step' => $step,
                '--seed' => $seed,
            ]);
            
            $output = ob_get_clean();
            $artisanOutput = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($artisanOutput);
                echo "\n\n" . '<span style="color: green;">Migrations completed successfully!</span>';
            } else {
                echo htmlspecialchars($artisanOutput);
                echo "\n\n" . '<span style="color: red;">Migrations failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</pre></div>';
    } else if ($action === 'migrate:status') {
        echo '<div class="section">
            <h3>Migration Status</h3>
            <pre>';
        
        try {
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:status');
            $output = \Illuminate\Support\Facades\Artisan::output();
            echo htmlspecialchars($output);
        } catch (\Exception $e) {
            echo '<span style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</pre></div>';
    } else if ($action === 'migrate:rollback') {
        echo '<div class="section">
            <h3>Rolling Back Migrations</h3>
            <pre>';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $step = isset($_POST['steps']) ? (int)$_POST['steps'] : 1;
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:rollback', [
                '--force' => $force,
                '--step' => $step,
            ]);
            
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: green;">Rollback completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: red;">Rollback failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</pre></div>';
    } else if ($action === 'migrate:fresh') {
        echo '<div class="section">
            <h3>Fresh Database Migration</h3>
            <pre>';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:fresh', [
                '--force' => $force,
                '--seed' => $seed,
            ]);
            
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: green;">Fresh migration completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: red;">Fresh migration failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</pre></div>';
    } else if ($action === 'migrate:refresh') {
        echo '<div class="section">
            <h3>Refreshing Migrations</h3>
            <pre>';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            $step = isset($_POST['steps']) ? (int)$_POST['steps'] : 0;
            
            $params = [
                '--force' => $force,
                '--seed' => $seed,
            ];
            
            if ($step > 0) {
                $params['--step'] = $step;
            }
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:refresh', $params);
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: green;">Refresh completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: red;">Refresh failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</pre></div>';
    }
}

// Show migration status
echo '<div class="section">
    <h3>Current Migration Status</h3>
    <p>This shows the current status of your migrations.</p>
';

try {
    ob_start();
    \Illuminate\Support\Facades\Artisan::call('migrate:status');
    $statusOutput = \Illuminate\Support\Facades\Artisan::output();
    ob_end_clean();

    // Parse the status output to create a nice table
    $lines = explode("\n", $statusOutput);
    $migrations = [];
    $headers = [];

    foreach ($lines as $i => $line) {
        if (empty(trim($line))) continue;

        // Parse header line
        if ($i === 0) {
            $headers = preg_split('/\s{2,}/', trim($line));
            continue;
        }

        // Parse data lines
        $data = preg_split('/\s{2,}/', trim($line));
        if (count($data) >= 3) {
            $migrations[] = [
                'status' => trim($data[0]),
                'migration' => trim($data[1]),
                'batch' => isset($data[2]) ? trim($data[2]) : '',
            ];
        }
    }

    // Display as a table
    if (!empty($migrations)) {
        echo '<table>
            <thead>
                <tr>
                    <th>Status</th>
                    <th>Migration</th>
                    <th>Batch</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($migrations as $migration) {
            $statusClass = $migration['status'] === 'Ran' ? 'badge-success' : 'badge-pending';
            echo '<tr>
                <td><span class="badge ' . $statusClass . '">' . htmlspecialchars($migration['status']) . '</span></td>
                <td>' . htmlspecialchars($migration['migration']) . '</td>
                <td>' . htmlspecialchars($migration['batch']) . '</td>
            </tr>';
        }

        echo '</tbody></table>';
    } else {
        echo '<p>No migration data available.</p>';
    }
} catch (\Exception $e) {
    echo '<div class="error">Error retrieving migration status: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

echo '</div>';

// Migration actions
echo '<div class="section">
    <h3>Migration Actions</h3>

    <h4>Run Migrations</h4>
    <p>Run pending migrations to update your database schema.</p>
    <form method="post" style="margin-bottom: 20px;">
        <input type="hidden" name="action" value="migrate">
        <div class="form-group">
            <label><input type="checkbox" name="force" value="yes"> Force (run in production)</label>
            <label><input type="checkbox" name="step" value="yes"> Step (run migrations incrementally)</label>
            <label><input type="checkbox" name="seed" value="yes"> Seed (run seeders after migration)</label>
        </div>
        <button type="submit">Run Migrations</button>
    </form>

    <h4>Rollback Migrations</h4>
    <p>Rollback the last batch of migrations.</p>
    <form method="post" style="margin-bottom: 20px;">
        <input type="hidden" name="action" value="migrate:rollback">
        <div class="form-group">
            <label><input type="checkbox" name="force" value="yes"> Force (run in production)</label>
            <label>Steps (number of batches to rollback): <input type="number" name="steps" value="1" min="1"></label>
        </div>
        <button type="submit">Rollback Migrations</button>
    </form>

    <h4>Refresh Migrations</h4>
    <p>Rollback all migrations and run them again.</p>
    <form method="post" style="margin-bottom: 20px;">
        <input type="hidden" name="action" value="migrate:refresh">
        <div class="form-group">
            <label><input type="checkbox" name="force" value="yes"> Force (run in production)</label>
            <label><input type="checkbox" name="seed" value="yes"> Seed (run seeders after migration)</label>
            <label>Steps (limit the number of migrations to be rolled back): <input type="number" name="steps" value="0" min="0"> (0 means all migrations)</label>
        </div>
        <button type="submit">Refresh Migrations</button>
    </form>

    <h4>Fresh Migrations</h4>
    <p><strong>DANGER:</strong> Drop all tables and run migrations from scratch.</p>
    <p style="color: red; font-weight: bold;">This will delete all data in your database!</p>
    <form method="post" style="margin-bottom: 20px;">
        <input type="hidden" name="action" value="migrate:fresh">
        <div class="form-group">
            <label><input type="checkbox" name="force" value="yes"> Force (run in production)</label>
            <label><input type="checkbox" name="seed" value="yes"> Seed (run seeders after migration)</label>
        </div>
        <button type="submit" class="danger">Fresh Migrations</button>
    </form>

    <h4>Security Notice</h4>
    <div class="warning">
        <p><strong>⚠️ This script provides direct access to modify your database structure. For security:</strong></p>
        <ul>
            <li>Delete this file immediately after use</li>
            <li>Consider password-protecting this directory</li>
            <li>Always backup your database before running migrations</li>
        </ul>
    </div>
</div>

    </div>
</body>
</html>';
