"""
Military-Grade Code Obfuscator
Main obfuscation engine with multi-layer protection capabilities.
"""

import os
import re
import random
import string
import hashlib
from typing import Dict, List, Tuple, Any
from pathlib import Path

from .config import ProtectionConfig
from .parser import FileParser
from .encryption import Encryption<PERSON>ngine
from .anti_debug import AntiDebugEngine

class MilitaryObfuscator:
    """Main obfuscation engine for military-grade code protection."""
    
    def __init__(self, config: ProtectionConfig):
        """
        Initialize the obfuscator.
        
        Args:
            config (ProtectionConfig): Configuration settings
        """
        self.config = config
        self.parser = FileParser()
        self.encryption = EncryptionEngine(config)
        self.anti_debug = AntiDebugEngine(config)
        
        # Obfuscation state
        self.variable_map = {}
        self.function_map = {}
        self.class_map = {}
        self.string_map = {}
        
        # Statistics
        self.stats = {
            'original_size': 0,
            'protected_size': 0,
            'variables_renamed': 0,
            'functions_renamed': 0,
            'strings_encoded': 0,
            'dead_code_injected': 0
        }
        
    def protect_file(self, input_path: str, output_path: str) -> Dict[str, Any]:
        """
        Apply military-grade protection to a file.
        
        Args:
            input_path (str): Path to input file
            output_path (str): Path for protected output
            
        Returns:
            dict: Protection results and statistics
        """
        try:
            # Read and parse input file
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            self.stats['original_size'] = len(content)
            
            # Determine file type and apply appropriate protection
            file_ext = Path(input_path).suffix.lower()
            
            if file_ext in ['.html', '.htm']:
                protected_content = self._protect_html(content)
            elif file_ext == '.css':
                protected_content = self._protect_css(content)
            elif file_ext in ['.js', '.jsx']:
                protected_content = self._protect_javascript(content)
            elif file_ext in ['.ts', '.tsx']:
                protected_content = self._protect_typescript(content)
            elif file_ext == '.php':
                protected_content = self._protect_php(content)
            else:
                # Generic text protection
                protected_content = self._protect_generic(content)
                
            # Apply encryption layers
            if self.config.encryption.base64_encoding or self.config.encryption.aes_encryption:
                protected_content = self.encryption.apply_encryption(protected_content, file_ext)
                
            # Inject anti-debugging measures
            if self.config.security.anti_debugging:
                protected_content = self.anti_debug.inject_protection(protected_content, file_ext)
                
            self.stats['protected_size'] = len(protected_content)
            
            # Write protected file
            output_dir = os.path.dirname(output_path)
            if output_dir:  # Only create directory if there is one
                os.makedirs(output_dir, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(protected_content)
                
            # Calculate protection level
            protection_level = self.config.get_protection_level()
            
            # Ensure sizes are integers for calculation
            original_size = int(self.stats['original_size'])
            protected_size = int(self.stats['protected_size'])

            return {
                'status': 'success',
                'input_path': input_path,
                'output_path': output_path,
                'original_size': original_size,
                'protected_size': protected_size,
                'compression_ratio': protected_size / original_size if original_size > 0 else 1,
                'protection_level': protection_level,
                'statistics': self.stats.copy()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'input_path': input_path
            }
            
    def _protect_html(self, content: str) -> str:
        """Apply protection to HTML content."""
        # Parse HTML structure
        parsed = self.parser.parse_html(content)
        
        # Extract and protect inline CSS
        content = self._protect_inline_css(content)
        
        # Extract and protect inline JavaScript
        content = self._protect_inline_javascript(content)
        
        # Obfuscate HTML attributes and IDs (carefully)
        if self.config.obfuscation.rename_variables:
            content = self._obfuscate_html_attributes(content)
            
        # Remove comments if enabled
        if self.config.obfuscation.remove_comments:
            content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
            
        # Minify if enabled
        if self.config.obfuscation.minify_code:
            content = self._minify_html(content)
            
        return content
        
    def _protect_css(self, content: str) -> str:
        """Apply protection to CSS content."""
        # Remove comments
        if self.config.obfuscation.remove_comments:
            content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
            
        # Obfuscate class names and IDs
        if self.config.obfuscation.rename_variables:
            content = self._obfuscate_css_selectors(content)
            
        # Encode string values
        if self.config.obfuscation.string_encoding:
            content = self._encode_css_strings(content)
            
        # Inject dead CSS rules
        if self.config.obfuscation.dead_code_injection:
            content = self._inject_dead_css(content)
            
        # Minify
        if self.config.obfuscation.minify_code:
            content = self._minify_css(content)
            
        return content
        
    def _protect_javascript(self, content: str) -> str:
        """Apply protection to JavaScript content."""
        # Remove comments
        if self.config.obfuscation.remove_comments:
            content = self._remove_js_comments(content)
            
        # Obfuscate variable and function names
        if self.config.obfuscation.rename_variables:
            content = self._obfuscate_js_identifiers(content)
            
        # Encode strings
        if self.config.obfuscation.string_encoding:
            content = self._encode_js_strings(content)
            
        # Apply control flow obfuscation
        if self.config.obfuscation.control_flow_obfuscation:
            content = self._obfuscate_control_flow(content)
            
        # Inject dead code
        if self.config.obfuscation.dead_code_injection:
            content = self._inject_dead_js_code(content)
            
        # Minify
        if self.config.obfuscation.minify_code:
            content = self._minify_javascript(content)
            
        return content
        
    def _protect_typescript(self, content: str) -> str:
        """Apply protection to TypeScript content."""
        # TypeScript protection is similar to JavaScript
        # but we need to preserve type annotations carefully
        return self._protect_javascript(content)
        
    def _protect_php(self, content: str) -> str:
        """Apply protection to PHP content."""
        # Remove comments
        if self.config.obfuscation.remove_comments:
            content = self._remove_php_comments(content)
            
        # Obfuscate variable names
        if self.config.obfuscation.rename_variables:
            content = self._obfuscate_php_variables(content)
            
        # Encode strings
        if self.config.obfuscation.string_encoding:
            content = self._encode_php_strings(content)
            
        # Inject dead code
        if self.config.obfuscation.dead_code_injection:
            content = self._inject_dead_php_code(content)
            
        return content
        
    def _protect_generic(self, content: str) -> str:
        """Apply generic protection to unknown file types."""
        # Basic string encoding
        if self.config.obfuscation.string_encoding:
            content = self._encode_generic_strings(content)
            
        return content
        
    def _generate_random_name(self, prefix: str = "", length: int = 8) -> str:
        """Generate a random obfuscated name."""
        chars = string.ascii_letters + string.digits
        random_part = ''.join(random.choices(chars, k=length))
        return f"{prefix}{random_part}"
        
    def _obfuscate_html_attributes(self, content: str) -> str:
        """Obfuscate HTML attributes while preserving functionality."""
        # This is a simplified version - in practice, we need to be very careful
        # about which attributes to obfuscate to avoid breaking functionality
        
        # Find and replace id attributes (but preserve those used in JavaScript)
        id_pattern = r'id=["\']([^"\']+)["\']'
        
        def replace_id(match):
            original_id = match.group(1)
            if original_id not in self.variable_map:
                # Check if this ID is used in JavaScript - if so, don't obfuscate
                # This is a simplified check
                if 'getElementById' in content and original_id in content:
                    return match.group(0)  # Don't obfuscate
                self.variable_map[original_id] = self._generate_random_name("id_")
            return f'id="{self.variable_map[original_id]}"'
            
        content = re.sub(id_pattern, replace_id, content)
        return content
        
    def _minify_html(self, content: str) -> str:
        """Minify HTML content."""
        if not self.config.processing.preserve_formatting:
            # Remove extra whitespace
            content = re.sub(r'\s+', ' ', content)
            content = re.sub(r'>\s+<', '><', content)
            
        return content.strip()
        
    def _minify_css(self, content: str) -> str:
        """Minify CSS content."""
        if not self.config.processing.preserve_formatting:
            # Remove whitespace and newlines
            content = re.sub(r'\s+', ' ', content)
            content = re.sub(r';\s*}', '}', content)
            content = re.sub(r'{\s*', '{', content)
            content = re.sub(r';\s*', ';', content)
            
        return content.strip()
        
    def _minify_javascript(self, content: str) -> str:
        """Minify JavaScript content."""
        if not self.config.processing.preserve_formatting:
            # Basic minification - remove extra whitespace
            content = re.sub(r'\s+', ' ', content)
            content = re.sub(r';\s*', ';', content)
            content = re.sub(r'{\s*', '{', content)
            content = re.sub(r'}\s*', '}', content)
            
        return content.strip()
        
    # Placeholder methods for more advanced obfuscation techniques
    # These will be implemented in subsequent phases
    
    def _protect_inline_css(self, content: str) -> str:
        """Protect CSS within <style> tags."""
        def replace_style(match):
            css_content = match.group(1)
            protected_css = self._protect_css(css_content)
            return f"<style{match.group(0)[6:match.start(1)-6]}{protected_css}</style>"
            
        return re.sub(r'<style[^>]*>(.*?)</style>', replace_style, content, flags=re.DOTALL)
        
    def _protect_inline_javascript(self, content: str) -> str:
        """Protect JavaScript within <script> tags."""
        def replace_script(match):
            js_content = match.group(1)
            protected_js = self._protect_javascript(js_content)
            return f"<script{match.group(0)[7:match.start(1)-7]}{protected_js}</script>"
            
        return re.sub(r'<script[^>]*>(.*?)</script>', replace_script, content, flags=re.DOTALL)
        
    # Additional methods will be implemented as we progress through the phases
    def _obfuscate_css_selectors(self, content: str) -> str:
        """Obfuscate CSS class and ID selectors."""
        # Find class selectors
        class_pattern = r'\.([a-zA-Z_][a-zA-Z0-9_-]*)'

        def replace_class(match):
            original_class = match.group(1)
            if original_class not in self.class_map:
                self.class_map[original_class] = self._generate_random_name("c", 6)
            return '.' + self.class_map[original_class]

        content = re.sub(class_pattern, replace_class, content)

        # Find ID selectors
        id_pattern = r'#([a-zA-Z_][a-zA-Z0-9_-]*)'

        def replace_id(match):
            original_id = match.group(1)
            if original_id not in self.variable_map:
                self.variable_map[original_id] = self._generate_random_name("i", 6)
            return '#' + self.variable_map[original_id]

        content = re.sub(id_pattern, replace_id, content)

        return content

    def _encode_css_strings(self, content: str) -> str:
        """Encode CSS string values and colors."""
        # Encode color values
        color_pattern = r'#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})'

        def encode_color(match):
            color = match.group(1)
            if len(color) == 6:
                # Convert hex to rgb
                r = int(color[0:2], 16)
                g = int(color[2:4], 16)
                b = int(color[4:6], 16)
                return f'rgb({r},{g},{b})'
            return match.group(0)

        content = re.sub(color_pattern, encode_color, content)

        # Encode font names and other string values
        string_pattern = r'(["\'])([^"\']+)\1'

        def encode_string(match):
            quote = match.group(1)
            string_content = match.group(2)

            # Simple encoding for CSS strings
            if len(string_content) > 3:
                # Use CSS unicode escapes for some characters
                encoded = ""
                for char in string_content:
                    if random.random() < 0.3 and ord(char) > 32:
                        encoded += f"\\{ord(char):04x}"
                    else:
                        encoded += char
                return quote + encoded + quote
            return match.group(0)

        content = re.sub(string_pattern, encode_string, content)

        return content

    def _inject_dead_css(self, content: str) -> str:
        """Inject dead CSS rules."""
        if not self.config.obfuscation.dead_code_injection:
            return content

        dead_css_rules = [
            f".{self._generate_random_name('dead', 6)} {{ display: none; }}",
            f"#{self._generate_random_name('unused', 6)} {{ visibility: hidden; }}",
            f".{self._generate_random_name('fake', 6)} {{ position: absolute; left: -9999px; }}",
            f"@media (max-width: 0px) {{ .{self._generate_random_name('never', 6)} {{ color: red; }} }}",
            f".{self._generate_random_name('empty', 6)} {{ /* empty rule */ }}",
        ]

        # Calculate how much dead CSS to inject
        dead_code_percentage = float(self.config.obfuscation.dead_code_percentage)
        dead_code_count = int(len(content.split('}')) * dead_code_percentage / 100)

        # Insert dead CSS rules at random positions
        for _ in range(dead_code_count):
            dead_rule = random.choice(dead_css_rules)
            # Insert after random closing brace
            content = content.replace('}', '}' + dead_rule, 1)
            self.stats['dead_code_injected'] += 1

        return content
        
    def _remove_js_comments(self, content: str) -> str:
        """Remove JavaScript comments."""
        # Remove single-line comments
        content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
        # Remove multi-line comments
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        return content
        
    def _obfuscate_js_identifiers(self, content: str) -> str:
        """Obfuscate JavaScript identifiers (variables, functions)."""
        # Find variable declarations
        var_pattern = r'\b(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)'

        def replace_var(match):
            original_name = match.group(1)
            if original_name not in self.variable_map:
                self.variable_map[original_name] = self._generate_random_name("_", 6)
                self.stats['variables_renamed'] += 1
            return match.group(0).replace(original_name, self.variable_map[original_name])

        content = re.sub(var_pattern, replace_var, content)

        # Replace variable usage
        for original, obfuscated in self.variable_map.items():
            # Use word boundaries to avoid partial replacements
            content = re.sub(r'\b' + re.escape(original) + r'\b', obfuscated, content)

        return content

    def _encode_js_strings(self, content: str) -> str:
        """Encode JavaScript string literals."""
        string_pattern = r'(["\'])(?:(?=(\\?))\2.)*?\1'

        def encode_string(match):
            original_string = match.group(0)
            quote_char = original_string[0]
            string_content = original_string[1:-1]

            if len(string_content) > 2:  # Only encode longer strings
                # Convert to array of character codes
                char_codes = [str(ord(c)) for c in string_content]
                encoded = f"String.fromCharCode({','.join(char_codes)})"
                self.stats['strings_encoded'] += 1
                return encoded
            return original_string

        return re.sub(string_pattern, encode_string, content)

    def _obfuscate_control_flow(self, content: str) -> str:
        """Apply control flow obfuscation."""
        if not self.config.obfuscation.control_flow_obfuscation:
            return content

        # Add fake conditional branches
        fake_conditions = [
            "if (Math.random() < 0) { console.log('never executed'); }",
            "while (false) { break; }",
            "if (1 === 2) { return; }",
            "switch (0) { case 1: break; default: break; }"
        ]

        # Insert fake conditions at random positions
        lines = content.split('\n')
        for i in range(0, len(lines), random.randint(5, 15)):
            if i < len(lines):
                fake_condition = random.choice(fake_conditions)
                lines.insert(i, fake_condition)

        return '\n'.join(lines)

    def _inject_dead_js_code(self, content: str) -> str:
        """Inject dead JavaScript code."""
        if not self.config.obfuscation.dead_code_injection:
            return content

        dead_code_snippets = [
            "var _unused = Math.floor(Math.random() * 1000);",
            "function _deadFunction() { return 'never called'; }",
            "var _temp = new Date().getTime();",
            "if (false) { console.log('dead code'); }",
            "var _array = [1, 2, 3, 4, 5];",
            "function _helper() { return _unused + _temp; }",
            "var _obj = { prop: 'value', method: function() {} };",
        ]

        # Calculate how much dead code to inject
        dead_code_percentage = float(self.config.obfuscation.dead_code_percentage)
        dead_code_count = int(len(content.split('\n')) * dead_code_percentage / 100)

        lines = content.split('\n')
        for _ in range(dead_code_count):
            position = random.randint(0, len(lines))
            dead_code = random.choice(dead_code_snippets)
            lines.insert(position, dead_code)
            self.stats['dead_code_injected'] += 1

        return '\n'.join(lines)
        
    def _remove_php_comments(self, content: str) -> str:
        """Remove PHP comments."""
        # Remove single-line comments
        content = re.sub(r'//.*$', '', content, flags=re.MULTILINE)
        content = re.sub(r'#.*$', '', content, flags=re.MULTILINE)
        # Remove multi-line comments
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        return content
        
    def _obfuscate_php_variables(self, content: str) -> str:
        """Obfuscate PHP variable names."""
        # Find PHP variable declarations
        var_pattern = r'\$([a-zA-Z_][a-zA-Z0-9_]*)'

        def replace_var(match):
            original_name = match.group(1)
            # Skip superglobals and common variables
            if original_name in ['_GET', '_POST', '_SESSION', '_COOKIE', '_SERVER', '_FILES', '_ENV', 'GLOBALS']:
                return match.group(0)

            if original_name not in self.variable_map:
                self.variable_map[original_name] = self._generate_random_name("_", 8)
                self.stats['variables_renamed'] += 1
            return '$' + self.variable_map[original_name]

        return re.sub(var_pattern, replace_var, content)

    def _encode_php_strings(self, content: str) -> str:
        """Encode PHP string literals."""
        # Find string literals
        string_pattern = r'(["\'])(?:(?=(\\?))\2.)*?\1'

        def encode_string(match):
            original_string = match.group(0)
            quote_char = original_string[0]
            string_content = original_string[1:-1]

            if len(string_content) > 3:  # Only encode longer strings
                # Convert to base64 and decode at runtime
                import base64
                encoded_content = base64.b64encode(string_content.encode()).decode()
                encoded = f'base64_decode("{encoded_content}")'
                self.stats['strings_encoded'] += 1
                return encoded
            return original_string

        return re.sub(string_pattern, encode_string, content)

    def _inject_dead_php_code(self, content: str) -> str:
        """Inject dead PHP code."""
        if not self.config.obfuscation.dead_code_injection:
            return content

        dead_code_snippets = [
            "$_unused = rand(1, 1000);",
            "function _deadFunction() { return 'never called'; }",
            "$_temp = time();",
            "if (false) { echo 'dead code'; }",
            "$_array = array(1, 2, 3, 4, 5);",
            "class _DeadClass { public function method() {} }",
            "$_obj = new stdClass();",
            "// " + self._generate_random_name("comment_", 10),
        ]

        # Calculate how much dead code to inject
        dead_code_percentage = float(self.config.obfuscation.dead_code_percentage)
        dead_code_count = int(len(content.split('\n')) * dead_code_percentage / 100)

        lines = content.split('\n')
        for _ in range(dead_code_count):
            position = random.randint(0, len(lines))
            dead_code = random.choice(dead_code_snippets)
            lines.insert(position, dead_code)
            self.stats['dead_code_injected'] += 1

        return '\n'.join(lines)
        
    def _encode_generic_strings(self, content: str) -> str:
        """Placeholder for generic string encoding."""
        return content
