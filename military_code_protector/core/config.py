"""
Configuration Management for Military-Grade Code Protection
Handles all protection settings and security parameters.
"""

import json
import os
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class ObfuscationSettings:
    """Settings for code obfuscation techniques."""
    # Basic obfuscation
    minify_code: bool = True
    remove_comments: bool = True
    remove_whitespace: bool = True
    rename_variables: bool = True
    
    # Advanced obfuscation
    string_encoding: bool = True
    control_flow_obfuscation: bool = True
    dead_code_injection: bool = True
    function_splitting: bool = True
    
    # Obfuscation strength (1-10)
    strength_level: int = 7
    dead_code_percentage: int = 50
    encoding_layers: int = 3

@dataclass
class EncryptionSettings:
    """Settings for encryption and encoding."""
    base64_encoding: bool = True
    hex_encoding: bool = True
    aes_encryption: bool = False
    custom_key_generation: bool = True
    
    # Encryption parameters
    aes_key_size: int = 256
    encryption_layers: int = 2
    dynamic_keys: bool = True

@dataclass
class SecuritySettings:
    """Settings for anti-debugging and security measures."""
    anti_debugging: bool = True
    integrity_checking: bool = True
    domain_locking: bool = False
    code_expiration: bool = False
    
    # Security parameters
    allowed_domains: List[str] = None
    expiration_date: str = None  # ISO format: YYYY-MM-DD
    tamper_detection: bool = True
    console_protection: bool = True

@dataclass
class ProcessingSettings:
    """Settings for file processing."""
    create_backup: bool = True
    preserve_formatting: bool = False
    batch_processing: bool = True
    
    # File handling
    backup_directory: str = "backups"
    output_suffix: str = "_protected"
    supported_extensions: List[str] = None

class ProtectionConfig:
    """Main configuration class for the protection system."""
    
    def __init__(self, config_file: str = None):
        """
        Initialize configuration.
        
        Args:
            config_file (str): Path to configuration file
        """
        self.config_file = config_file or "protection_config.json"
        
        # Initialize default settings
        self.obfuscation = ObfuscationSettings()
        self.encryption = EncryptionSettings()
        self.security = SecuritySettings()
        self.processing = ProcessingSettings()
        
        # Set default supported extensions
        if self.processing.supported_extensions is None:
            self.processing.supported_extensions = ['.html', '.css', '.js', '.php', '.htm', '.jsx', '.ts', '.tsx']
            
        if self.security.allowed_domains is None:
            self.security.allowed_domains = []
            
        # Load configuration if file exists
        self.load_config()
        
    def load_config(self) -> None:
        """Load configuration from file."""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                    
                # Update settings from loaded data
                if 'obfuscation' in config_data:
                    self._update_dataclass(self.obfuscation, config_data['obfuscation'])
                if 'encryption' in config_data:
                    self._update_dataclass(self.encryption, config_data['encryption'])
                if 'security' in config_data:
                    self._update_dataclass(self.security, config_data['security'])
                if 'processing' in config_data:
                    self._update_dataclass(self.processing, config_data['processing'])
                    
            except Exception as e:
                print(f"Warning: Could not load config file {self.config_file}: {e}")
                
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            config_data = {
                'obfuscation': asdict(self.obfuscation),
                'encryption': asdict(self.encryption),
                'security': asdict(self.security),
                'processing': asdict(self.processing)
            }
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_file) or '.', exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            print(f"Warning: Could not save config file {self.config_file}: {e}")
            
    def update(self, updates: Dict[str, Any]) -> None:
        """
        Update configuration with new values.
        
        Args:
            updates (dict): Dictionary of updates to apply
        """
        for section, values in updates.items():
            if hasattr(self, section):
                section_obj = getattr(self, section)
                self._update_dataclass(section_obj, values)
            else:
                # Handle direct updates
                if section == 'strength':
                    # Convert strength level from string to integer
                    if isinstance(values, str):
                        strength_map = {
                            'low': 3,
                            'medium': 7,
                            'high': 10
                        }
                        self.obfuscation.strength_level = strength_map.get(values.lower(), 7)
                    else:
                        self.obfuscation.strength_level = int(values)
                elif section == 'create_backup':
                    self.processing.create_backup = values
                # Add more direct mappings as needed
                    
    def get_protection_level(self) -> int:
        """
        Calculate overall protection level percentage.
        
        Returns:
            int: Protection level (0-100)
        """
        factors = []
        
        # Obfuscation factors
        if self.obfuscation.minify_code:
            factors.append(5)
        if self.obfuscation.rename_variables:
            factors.append(10)
        if self.obfuscation.string_encoding:
            factors.append(15)
        if self.obfuscation.control_flow_obfuscation:
            factors.append(20)
        if self.obfuscation.dead_code_injection:
            factors.append(10)
            
        # Encryption factors
        if self.encryption.base64_encoding:
            factors.append(5)
        if self.encryption.hex_encoding:
            factors.append(5)
        if self.encryption.aes_encryption:
            factors.append(25)
        if self.encryption.custom_key_generation:
            factors.append(10)
            
        # Security factors
        if self.security.anti_debugging:
            factors.append(15)
        if self.security.integrity_checking:
            factors.append(10)
        if self.security.domain_locking:
            factors.append(10)
        if self.security.code_expiration:
            factors.append(5)
            
        # Apply strength multiplier
        base_level = min(sum(factors), 100)
        strength_level = float(self.obfuscation.strength_level)
        strength_multiplier = strength_level / 10.0

        return min(int(base_level * strength_multiplier), 100)
        
    def get_file_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return self.processing.supported_extensions.copy()
        
    def is_file_supported(self, file_path: str) -> bool:
        """
        Check if file type is supported.
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if file type is supported
        """
        file_ext = Path(file_path).suffix.lower()
        return file_ext in self.processing.supported_extensions
        
    def get_backup_path(self, original_path: str) -> str:
        """
        Generate backup path for a file.
        
        Args:
            original_path (str): Original file path
            
        Returns:
            str: Backup file path
        """
        original = Path(original_path)
        backup_dir = Path(self.processing.backup_directory)
        
        # Create backup directory structure
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate backup filename with timestamp
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{original.stem}_backup_{timestamp}{original.suffix}"
        
        return str(backup_dir / backup_name)
        
    def get_output_path(self, input_path: str, output_path: str = None) -> str:
        """
        Generate output path for protected file.
        
        Args:
            input_path (str): Input file path
            output_path (str): Custom output path (optional)
            
        Returns:
            str: Output file path
        """
        if output_path:
            return output_path
            
        input_file = Path(input_path)
        output_name = f"{input_file.stem}{self.processing.output_suffix}{input_file.suffix}"
        return str(input_file.parent / output_name)
        
    def _update_dataclass(self, obj, updates: Dict[str, Any]) -> None:
        """Update dataclass object with new values."""
        for key, value in updates.items():
            if hasattr(obj, key):
                # Get the current attribute to determine its type
                current_attr = getattr(obj, key)

                # Convert value to the correct type
                if isinstance(current_attr, bool):
                    # Handle boolean conversion
                    if isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = bool(value)
                elif isinstance(current_attr, int):
                    # Handle integer conversion
                    value = int(float(value)) if value is not None else 0
                elif isinstance(current_attr, float):
                    # Handle float conversion
                    value = float(value) if value is not None else 0.0

                setattr(obj, key, value)
                
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'obfuscation': asdict(self.obfuscation),
            'encryption': asdict(self.encryption),
            'security': asdict(self.security),
            'processing': asdict(self.processing)
        }
        
    def __str__(self) -> str:
        """String representation of configuration."""
        return f"ProtectionConfig(level={self.get_protection_level()}%, " \
               f"strength={self.obfuscation.strength_level}/10)"
