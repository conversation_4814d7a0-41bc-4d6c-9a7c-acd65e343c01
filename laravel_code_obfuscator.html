<!DOCTYPE html>
<!--
    Laravel Code Obfuscator Tool
    Copyright (c) 2024 Firoz Anam. All rights reserved.
    
    PROPRIETARY SOFTWARE - UNAUTHORIZED COPYING PROHIBITED
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Code Obfuscator - Advanced Protection Tool</title>
    <style>
        :root {
            --primary: #ff2d20;
            --primary-dark: #e02417;
            --secondary: #2d3748;
            --secondary-light: #4a5568;
            --accent: #667eea;
            --success: #48bb78;
            --warning: #ed8936;
            --danger: #f56565;
            --info: #4299e1;
            --light: #f7fafc;
            --dark: #1a202c;
            --text: #2d3748;
            --text-light: #718096;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 20px;
            padding: 25px 40px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 8px;
        }

        .header p {
            font-size: 1.1rem;
            color: var(--text-light);
            max-width: 600px;
            margin: 0 auto;
        }

        .tool-section {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text);
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .upload-zone {
            border: 3px dashed var(--border);
            border-radius: 16px;
            padding: 50px 30px;
            text-align: center;
            background: var(--light);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover {
            border-color: var(--primary);
            background: #fef5f5;
        }

        .upload-zone.dragover {
            border-color: var(--primary);
            background: #fef5f5;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            color: var(--text-light);
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: var(--text);
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .option-card {
            background: var(--light);
            border-radius: 12px;
            padding: 25px;
            border: 2px solid var(--border);
            transition: all 0.3s ease;
        }

        .option-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .option-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .option-description {
            color: var(--text-light);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
        }

        .checkbox-item:hover {
            background: #f8f9fa;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
        }

        .checkbox-item label {
            font-weight: 500;
            color: var(--text);
            cursor: pointer;
            flex: 1;
        }

        .strength-meter {
            background: var(--border);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .strength-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 50%, var(--success) 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .strength-label {
            text-align: center;
            font-weight: 600;
            margin-top: 10px;
            color: var(--text);
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            min-width: 180px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 45, 32, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary) 0%, var(--secondary-light) 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(45, 55, 72, 0.3);
        }

        .btn-encrypt {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .btn-encrypt:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success) 0%, #38a169 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(72, 187, 120, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .processing-queue {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: var(--shadow);
            display: none;
        }

        .queue-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 12px;
            background: var(--light);
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .queue-item:last-child {
            margin-bottom: 0;
        }

        .queue-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .queue-icon.pending {
            background: var(--warning);
        }

        .queue-icon.processing {
            background: var(--info);
            animation: pulse 1.5s infinite;
        }

        .queue-icon.completed {
            background: var(--success);
        }

        .queue-icon.error {
            background: var(--danger);
        }

        .queue-details {
            flex: 1;
        }

        .queue-filename {
            font-weight: 600;
            color: var(--text);
            margin-bottom: 5px;
        }

        .queue-status {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .queue-progress {
            background: var(--border);
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 8px;
        }

        .queue-progress-fill {
            height: 100%;
            background: var(--primary);
            width: 0%;
            transition: width 0.3s ease;
        }

        .queue-actions {
            display: flex;
            gap: 10px;
        }

        .queue-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .queue-btn.download {
            background: var(--success);
            color: white;
        }

        .queue-btn.remove {
            background: var(--danger);
            color: white;
        }

        .queue-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .advanced-options {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border: 2px solid var(--border);
        }

        .advanced-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .range-input {
            width: 100%;
            margin: 15px 0;
            accent-color: var(--primary);
        }

        .range-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: 500;
            color: var(--text);
        }

        .range-value {
            background: var(--primary);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-content {
            background: white;
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 10px;
        }

        .loading-subtext {
            color: var(--text-light);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: var(--secondary);
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .back-button:hover {
            background: var(--secondary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: white;
            text-decoration: none;
        }

        .security-features {
            background: linear-gradient(135deg, #fef5f5 0%, #fed7d7 100%);
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border-left: 5px solid var(--primary);
        }

        .security-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .security-list {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px 20px;
        }

        .security-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            color: var(--text);
        }

        .security-list li::before {
            content: '🔒';
            font-size: 1.2rem;
        }

        /* Responsive design - single column on smaller screens */
        @media (max-width: 768px) {
            .security-list {
                grid-template-columns: 1fr;
                gap: 10px;
            }
        }

        /* Encryption Configuration Panel */
        .encryption-config {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            padding: 25px;
            margin-top: 25px;
            border-left: 5px solid var(--accent);
        }

        .config-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .config-section h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Radio Button Styles */
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .radio-option {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .radio-option:hover {
            border-color: var(--accent);
            background: #f8fafc;
        }

        .radio-option input[type="radio"] {
            display: none;
        }

        .radio-custom {
            width: 20px;
            height: 20px;
            border: 2px solid #cbd5e1;
            border-radius: 50%;
            position: relative;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .radio-option input[type="radio"]:checked + .radio-custom {
            border-color: var(--accent);
            background: var(--accent);
        }

        .radio-option input[type="radio"]:checked + .radio-custom::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
        }

        .radio-content strong {
            display: block;
            color: var(--text);
            margin-bottom: 4px;
        }

        .radio-content small {
            color: var(--text-light);
            font-size: 0.85rem;
        }

        /* Toggle Switch Styles */
        .toggle-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .toggle-option {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-option:hover {
            border-color: var(--accent);
            background: #f8fafc;
        }

        .toggle-option input[type="checkbox"] {
            display: none;
        }

        .toggle-slider {
            width: 44px;
            height: 24px;
            background: #cbd5e1;
            border-radius: 12px;
            position: relative;
            flex-shrink: 0;
            margin-top: 2px;
            transition: all 0.3s ease;
        }

        .toggle-slider::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-option input[type="checkbox"]:checked + .toggle-slider {
            background: var(--accent);
        }

        .toggle-option input[type="checkbox"]:checked + .toggle-slider::after {
            transform: translateX(20px);
        }

        .toggle-content strong {
            display: block;
            color: var(--text);
            margin-bottom: 4px;
        }

        .toggle-content small {
            color: var(--text-light);
            font-size: 0.85rem;
        }

        /* Domain Configuration */
        .domain-config {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }

        .domain-config h4 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text);
            margin-bottom: 15px;
        }

        .domain-input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .domain-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .domain-input:focus {
            outline: none;
            border-color: var(--accent);
        }

        .add-domain-btn {
            padding: 10px 20px;
            background: var(--accent);
            color: white;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-domain-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .domain-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .domain-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f1f5f9;
            border-radius: 20px;
            font-size: 0.85rem;
            color: var(--text);
        }

        .remove-domain {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-domain:hover {
            background: #dc2626;
        }

        /* Encryption Results Styles */
        .encryption-result {
            border-left: 4px solid #10b981;
        }

        .encryption-info {
            margin-top: 8px;
            font-size: 0.85rem;
        }

        .encryption-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;
            color: var(--text-light);
        }

        .encryption-features {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .encryption-features span {
            background: #f0f9ff;
            color: #0369a1;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .queue-btn.encrypted {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .queue-btn.encrypted:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
        }

        .queue-icon.completed {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        /* Responsive encryption config */
        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }

            .encryption-stats {
                flex-direction: column;
                gap: 5px;
            }

            .encryption-features {
                justify-content: flex-start;
            }
        }

        .file-list {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .file-item:hover {
            transform: translateY(-1px);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            background: var(--primary);
            color: white;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: var(--text);
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .file-remove {
            background: var(--danger);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-remove:hover {
            background: #e53e3e;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .options-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔐 Advanced Code Obfuscator</h1>
            <p>Military-grade code protection with multi-layer obfuscation, encryption, and anti-debugging measures</p>
        </div>

        <!-- File Upload Section -->
        <div class="tool-section">
            <h2 class="section-title">
                <div class="section-icon">📁</div>
                File Upload & Processing
            </h2>
            
            <div class="upload-zone" id="uploadZone">
                <div class="upload-icon">📄</div>
                <div class="upload-text">Drop files here or click to browse</div>
                <div class="upload-subtext">Supports: HTML, CSS, JavaScript, PHP files</div>
                <input type="file" class="file-input" id="fileInput" multiple accept=".html,.css,.js,.php,.htm,.jsx,.ts,.tsx">
            </div>

            <div class="file-list" id="fileList" style="display: none;">
                <!-- File items will be added here -->
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="filesCount">0</div>
                    <div class="stat-label">Files Queued</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalSize">0 KB</div>
                    <div class="stat-label">Total Size</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completedCount">0</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="protectionLevel">85%</div>
                    <div class="stat-label">Protection Level</div>
                </div>
            </div>
        </div>

        <!-- Obfuscation Options -->
        <div class="tool-section">
            <h2 class="section-title">
                <div class="section-icon">⚙️</div>
                Obfuscation Configuration
            </h2>

            <div class="options-grid">
                <!-- Basic Obfuscation -->
                <div class="option-card">
                    <div class="option-title">
                        🔧 Basic Obfuscation
                    </div>
                    <div class="option-description">
                        Essential protection features for code security
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="minify" checked>
                            <label for="minify">Code Minification</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="removeComments" checked>
                            <label for="removeComments">Remove Comments</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="removeWhitespace" checked>
                            <label for="removeWhitespace">Remove Whitespace</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="renameVariables" checked>
                            <label for="renameVariables">Rename Variables</label>
                        </div>
                    </div>
                </div>

                <!-- Advanced Obfuscation -->
                <div class="option-card">
                    <div class="option-title">
                        🚀 Advanced Obfuscation
                    </div>
                    <div class="option-description">
                        Professional-grade protection mechanisms
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="stringEncoding" checked>
                            <label for="stringEncoding">String Encoding</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="controlFlow" checked>
                            <label for="controlFlow">Control Flow Obfuscation</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="deadCode" checked>
                            <label for="deadCode">Dead Code Injection</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="functionSplit" checked>
                            <label for="functionSplit">Function Splitting</label>
                        </div>
                    </div>
                </div>

                <!-- Security Features -->
                <div class="option-card">
                    <div class="option-title">
                        🛡️ Security Features
                    </div>
                    <div class="option-description">
                        Anti-debugging and integrity protection
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="antiDebug" checked>
                            <label for="antiDebug">Anti-Debugging</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="integrityCheck" checked>
                            <label for="integrityCheck">Integrity Checking</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="domainLock" checked>
                            <label for="domainLock">Domain Locking</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="expiration" checked>
                            <label for="expiration">Code Expiration</label>
                        </div>
                    </div>
                </div>

                <!-- Encryption Options -->
                <div class="option-card">
                    <div class="option-title">
                        🔐 Encryption Options
                    </div>
                    <div class="option-description">
                        Multi-layer encryption for maximum security
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="base64" checked>
                            <label for="base64">Base64 Encoding</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="aes">
                            <label for="aes">AES Encryption</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="hexEncode" checked>
                            <label for="hexEncode">Hex Encoding</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="customKey" checked>
                            <label for="customKey">Custom Key Generation</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Options -->
            <div class="advanced-options">
                <div class="advanced-title">
                    🎯 Advanced Configuration
                </div>
                
                <div class="range-label">
                    <span>Obfuscation Strength</span>
                    <span class="range-value" id="strengthValue">70%</span>
                </div>
                <input type="range" class="range-input" id="strengthRange" min="10" max="100" value="70">

                <div class="range-label">
                    <span>Dead Code Injection</span>
                    <span class="range-value" id="deadCodeValue">50%</span>
                </div>
                <input type="range" class="range-input" id="deadCodeRange" min="0" max="100" value="50">

                <div class="range-label">
                    <span>String Encoding Layers</span>
                    <span class="range-value" id="encodingValue">3</span>
                </div>
                <input type="range" class="range-input" id="encodingRange" min="1" max="10" value="3">
            </div>

            <!-- Protection Strength Meter -->
            <div class="strength-meter">
                <div class="strength-fill" id="strengthFill" style="width: 85%;"></div>
            </div>
            <div class="strength-label" id="strengthLabel">Maximum Protection</div>

            <!-- Security Features List -->
            <div class="security-features">
                <div class="security-title">
                    🔒 Active Security Features
                </div>
                <ul class="security-list" id="activeSecurityList">
                    <li>Multi-layer string encoding with dynamic keys</li>
                    <li>Variable name scrambling with random patterns</li>
                    <li>Dead code injection for reverse engineering protection</li>
                    <li>Anti-debugging measures with detection bypass</li>
                    <li>Control flow obfuscation with fake branches</li>
                    <li>Integrity checking with hash validation</li>
                    <li>Domain locking for authorized deployment only</li>
                    <li>Code expiration with time-based validation</li>
                </ul>
            </div>

            <!-- Encryption Configuration Panel -->
            <div class="encryption-config">
                <div class="config-title">
                    ⚙️ Encryption Configuration
                </div>
                <div class="config-grid">
                    <div class="config-section">
                        <h4>🔐 Encryption Level</h4>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="encryptionLevel" value="basic" checked>
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <strong>Basic Protection</strong>
                                    <small>Fast, reliable, production-safe</small>
                                </div>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="encryptionLevel" value="advanced">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <strong>Advanced Encryption</strong>
                                    <small>Multi-layer, high security</small>
                                </div>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="encryptionLevel" value="maximum">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <strong>Maximum Security</strong>
                                    <small>Military-grade protection</small>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="config-section">
                        <h4>🚀 Production Mode</h4>
                        <div class="toggle-group">
                            <label class="toggle-option">
                                <input type="checkbox" id="productionSafe" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <strong>Production-Safe Mode</strong>
                                    <small>Prevents HTTP 500 errors on servers</small>
                                </div>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="loadBalancerCompatible" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <strong>Load Balancer Compatible</strong>
                                    <small>Works with proxies and CDNs</small>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="config-section">
                        <h4>🛡️ Security Options</h4>
                        <div class="toggle-group">
                            <label class="toggle-option">
                                <input type="checkbox" id="antiDebugging" checked>
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <strong>Anti-Debugging</strong>
                                    <small>Blocks debugging attempts</small>
                                </div>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="integrityCheck">
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <strong>Integrity Checking</strong>
                                    <small>Detects file tampering</small>
                                </div>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="domainLocking">
                                <span class="toggle-slider"></span>
                                <div class="toggle-content">
                                    <strong>Domain Locking</strong>
                                    <small>Restrict to specific domains</small>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Domain Configuration (shown when domain locking is enabled) -->
                <div class="domain-config" id="domainConfig" style="display: none;">
                    <h4>🌐 Allowed Domains</h4>
                    <div class="domain-input-group">
                        <input type="text" id="domainInput" placeholder="Enter domain (e.g., yoursite.com)" class="domain-input">
                        <button type="button" id="addDomain" class="add-domain-btn">Add</button>
                    </div>
                    <div class="domain-list" id="domainList">
                        <div class="domain-item">
                            <span>localhost</span>
                            <button type="button" class="remove-domain">×</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-primary" id="startObfuscation" disabled>
                    🚀 Start Obfuscation
                </button>
                <button class="btn btn-encrypt" id="startEncryption" disabled>
                    🔒 Create Encrypted Files
                </button>
                <button class="btn btn-secondary" id="clearQueue">
                    🗑️ Clear Queue
                </button>
                <button class="btn btn-success" id="downloadAll" disabled>
                    📥 Download All
                </button>
            </div>
        </div>

        <!-- Processing Queue -->
        <div class="processing-queue" id="processingQueue">
            <h2 class="section-title">
                <div class="section-icon">⚡</div>
                Processing Queue
            </h2>
            <div id="queueList">
                <!-- Queue items will be added here dynamically -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Processing Files...</div>
            <div class="loading-subtext">Applying advanced obfuscation techniques</div>
        </div>
    </div>

    <script>
        // Core JavaScript functionality for the Laravel Code Obfuscator
        class CodeObfuscator {
            constructor() {
                this.selectedFiles = [];
                this.processedFiles = [];
                this.isProcessing = false;
                this.protectionLevel = 85;

                // API Configuration
                this.apiBaseUrl = 'http://127.0.0.1:8081';

                // Feature mapping for Active Security Features
                this.featureMapping = {
                    // Basic Obfuscation
                    'minify': 'Code minification and compression',
                    'removeComments': 'Comment removal for cleaner code',
                    'removeWhitespace': 'Whitespace elimination',
                    'renameVariables': 'Variable name scrambling with random patterns',

                    // Advanced Obfuscation
                    'stringEncoding': 'Multi-layer string encoding with dynamic keys',
                    'controlFlow': 'Control flow obfuscation with fake branches',
                    'deadCode': 'Dead code injection for reverse engineering protection',
                    'functionSplit': 'Function splitting and restructuring',

                    // Security Features
                    'antiDebug': 'Anti-debugging measures with detection bypass',
                    'integrityCheck': 'Integrity checking with hash validation',
                    'domainLock': 'Domain locking for authorized deployment only',
                    'expiration': 'Code expiration with time-based validation',

                    // Encryption Options
                    'base64': 'Base64 encoding layer',
                    'aes': 'AES encryption with secure keys',
                    'hexEncode': 'Hexadecimal encoding transformation',
                    'customKey': 'Custom key generation system'
                };

                this.initializeEventListeners();
                this.updateProtectionLevel();
                this.initializeEncryptionConfig();
            }

            initializeEventListeners() {
                // File upload functionality
                const fileInput = document.getElementById('fileInput');
                const uploadZone = document.getElementById('uploadZone');

                // File input change event
                fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

                // Drag and drop functionality
                uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
                uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
                uploadZone.addEventListener('drop', (e) => this.handleFileDrop(e));
                uploadZone.addEventListener('click', () => fileInput.click());

                // Range input listeners for real-time updates
                document.getElementById('strengthRange').addEventListener('input', (e) => {
                    document.getElementById('strengthValue').textContent = e.target.value + '%';
                    this.updateProtectionLevel();
                });

                document.getElementById('deadCodeRange').addEventListener('input', (e) => {
                    document.getElementById('deadCodeValue').textContent = e.target.value + '%';
                    this.updateProtectionLevel();
                });

                document.getElementById('encodingRange').addEventListener('input', (e) => {
                    document.getElementById('encodingValue').textContent = e.target.value;
                    this.updateProtectionLevel();
                });

                // Checkbox listeners for real-time updates
                const checkboxes = document.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.updateProtectionLevel());
                });

                // Button event listeners
                document.getElementById('startObfuscation').addEventListener('click', () => this.startObfuscation());
                document.getElementById('startEncryption').addEventListener('click', () => this.startEncryption());
                document.getElementById('clearQueue').addEventListener('click', () => this.clearQueue());
                document.getElementById('downloadAll').addEventListener('click', () => this.downloadAll());
            }

            handleFileSelect(event) {
                const files = Array.from(event.target.files);
                this.addFiles(files);
            }

            handleDragOver(event) {
                event.preventDefault();
                event.stopPropagation();
                document.getElementById('uploadZone').classList.add('dragover');
            }

            handleDragLeave(event) {
                event.preventDefault();
                event.stopPropagation();
                document.getElementById('uploadZone').classList.remove('dragover');
            }

            handleFileDrop(event) {
                event.preventDefault();
                event.stopPropagation();
                document.getElementById('uploadZone').classList.remove('dragover');

                const files = Array.from(event.dataTransfer.files);
                this.addFiles(files);
            }

            addFiles(files) {
                const validExtensions = ['.html', '.css', '.js', '.php', '.htm', '.jsx', '.ts', '.tsx'];

                files.forEach(file => {
                    const extension = '.' + file.name.split('.').pop().toLowerCase();

                    if (validExtensions.includes(extension)) {
                        // Check if file already exists
                        const existingFile = this.selectedFiles.find(f => f.name === file.name);
                        if (!existingFile) {
                            this.selectedFiles.push(file);
                        }
                    } else {
                        this.showNotification(`File ${file.name} is not supported`, 'warning');
                    }
                });

                this.updateFileList();
                this.updateStats();
                this.updateButtons();
            }

            updateFileList() {
                const fileList = document.getElementById('fileList');
                const fileListContainer = fileList;

                if (this.selectedFiles.length === 0) {
                    fileList.style.display = 'none';
                    return;
                }

                fileList.style.display = 'block';
                fileListContainer.innerHTML = '';

                this.selectedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <div class="file-info">
                            <div class="file-icon">${this.getFileExtension(file.name).toUpperCase()}</div>
                            <div class="file-details">
                                <div class="file-name">${file.name}</div>
                                <div class="file-size">${this.formatFileSize(file.size)}</div>
                            </div>
                        </div>
                        <button class="file-remove" onclick="obfuscator.removeFile(${index})">Remove</button>
                    `;
                    fileListContainer.appendChild(fileItem);
                });
            }

            removeFile(index) {
                this.selectedFiles.splice(index, 1);
                this.updateFileList();
                this.updateStats();
                this.updateButtons();
            }

            updateStats() {
                const filesCount = document.getElementById('filesCount');
                const totalSize = document.getElementById('totalSize');
                const completedCount = document.getElementById('completedCount');

                filesCount.textContent = this.selectedFiles.length;

                const totalBytes = this.selectedFiles.reduce((sum, file) => sum + file.size, 0);
                totalSize.textContent = this.formatFileSize(totalBytes);

                completedCount.textContent = this.processedFiles.length;
            }

            updateButtons() {
                const startButton = document.getElementById('startObfuscation');
                const encryptButton = document.getElementById('startEncryption');
                const downloadButton = document.getElementById('downloadAll');

                startButton.disabled = this.selectedFiles.length === 0 || this.isProcessing;
                encryptButton.disabled = this.selectedFiles.length === 0 || this.isProcessing;
                downloadButton.disabled = this.processedFiles.length === 0;
            }

            updateProtectionLevel() {
                let level = 0;

                // Base level from checkboxes
                const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
                level += checkboxes.length * 8; // Each checkbox adds 8%

                // Strength multiplier
                const strength = parseInt(document.getElementById('strengthRange').value);
                level = Math.min(level * (strength / 100), 100);

                this.protectionLevel = Math.round(level);

                // Update UI
                document.getElementById('protectionLevel').textContent = this.protectionLevel + '%';

                const strengthFill = document.getElementById('strengthFill');
                const strengthLabel = document.getElementById('strengthLabel');

                strengthFill.style.width = this.protectionLevel + '%';

                if (this.protectionLevel < 30) {
                    strengthLabel.textContent = 'Basic Protection';
                } else if (this.protectionLevel < 70) {
                    strengthLabel.textContent = 'Medium Protection';
                } else {
                    strengthLabel.textContent = 'Maximum Protection';
                }

                // Update Active Security Features list
                this.updateActiveSecurityFeatures();
            }

            updateActiveSecurityFeatures() {
                const activeSecurityList = document.getElementById('activeSecurityList');
                if (!activeSecurityList) return;

                // Get all checked checkboxes
                const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked');
                const activeFeatures = [];

                // Map checked checkboxes to their feature descriptions
                checkedBoxes.forEach(checkbox => {
                    const featureId = checkbox.id;
                    if (this.featureMapping[featureId]) {
                        activeFeatures.push(this.featureMapping[featureId]);
                    }
                });

                // Update the list
                if (activeFeatures.length === 0) {
                    activeSecurityList.innerHTML = '<li style="color: #666; font-style: italic;">No security features selected</li>';
                } else {
                    activeSecurityList.innerHTML = activeFeatures
                        .map(feature => `<li>${feature}</li>`)
                        .join('');
                }
            }

            getFileExtension(filename) {
                return filename.split('.').pop() || '';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }

            showNotification(message, type = 'info') {
                // Create a simple notification
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    background: ${type === 'warning' ? '#ed8936' : '#4299e1'};
                    color: white;
                    border-radius: 8px;
                    z-index: 1001;
                    font-weight: 600;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            clearQueue() {
                this.selectedFiles = [];
                this.processedFiles = [];
                this.updateFileList();
                this.updateStats();
                this.updateButtons();

                // Clear file input
                document.getElementById('fileInput').value = '';

                // Hide processing queue
                document.getElementById('processingQueue').style.display = 'none';

                this.showNotification('Queue cleared successfully', 'info');
            }

            async startObfuscation() {
                if (this.selectedFiles.length === 0) {
                    this.showNotification('Please select files to obfuscate', 'warning');
                    return;
                }

                this.isProcessing = true;
                this.updateButtons();

                // Show loading overlay
                document.getElementById('loadingOverlay').style.display = 'flex';

                // Show processing queue
                const processingQueue = document.getElementById('processingQueue');
                processingQueue.style.display = 'block';

                const queueList = document.getElementById('queueList');
                queueList.innerHTML = '';

                try {
                    // Check if API is available
                    if (typeof protectorAPI !== 'undefined') {
                        // Use the military-grade protection API
                        await this.processWithAPI();
                    } else {
                        // Fallback to client-side processing
                        await this.processClientSide();
                    }
                } catch (error) {
                    console.error('Processing error:', error);
                    this.showNotification('Processing failed: ' + error.message, 'warning');
                } finally {
                    this.isProcessing = false;
                    this.updateButtons();
                    document.getElementById('loadingOverlay').style.display = 'none';
                }
            }

            async startEncryption() {
                if (this.selectedFiles.length === 0) {
                    this.showNotification('Please select files to encrypt', 'warning');
                    return;
                }

                this.isProcessing = true;
                this.updateButtons();

                // Show loading overlay
                document.getElementById('loadingOverlay').style.display = 'flex';

                // Show processing queue
                const processingQueue = document.getElementById('processingQueue');
                processingQueue.style.display = 'block';

                const queueList = document.getElementById('queueList');
                queueList.innerHTML = '';

                try {
                    // Get encryption configuration
                    const encryptionConfig = this.getEncryptionConfiguration();

                    // Show processing notification
                    this.showNotification('🔒 Starting encryption process...', 'info');

                    // Process with custom encryption
                    await this.processWithCustomEncryption(encryptionConfig);

                } catch (error) {
                    console.error('Encryption error:', error);
                    this.showNotification('Encryption failed: ' + error.message, 'error');
                } finally {
                    this.isProcessing = false;
                    this.updateButtons();
                    document.getElementById('loadingOverlay').style.display = 'none';
                }
            }

            initializeEncryptionConfig() {
                // Initialize encryption configuration listeners
                const encryptionLevels = document.querySelectorAll('input[name="encryptionLevel"]');
                const domainLockingToggle = document.getElementById('domainLocking');
                const domainConfig = document.getElementById('domainConfig');
                const addDomainBtn = document.getElementById('addDomain');
                const domainInput = document.getElementById('domainInput');

                // Encryption level change handler
                encryptionLevels.forEach(radio => {
                    radio.addEventListener('change', () => {
                        this.updateEncryptionSettings();
                    });
                });

                // Domain locking toggle
                domainLockingToggle.addEventListener('change', (e) => {
                    domainConfig.style.display = e.target.checked ? 'block' : 'none';
                });

                // Add domain functionality
                addDomainBtn.addEventListener('click', () => {
                    this.addDomain();
                });

                domainInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.addDomain();
                    }
                });

                // Initialize domain list event listeners
                this.initializeDomainListeners();

                // Update initial settings
                this.updateEncryptionSettings();
            }

            updateEncryptionSettings() {
                const selectedLevel = document.querySelector('input[name="encryptionLevel"]:checked').value;
                const productionSafe = document.getElementById('productionSafe').checked;
                const loadBalancerCompatible = document.getElementById('loadBalancerCompatible').checked;
                const antiDebugging = document.getElementById('antiDebugging').checked;
                const integrityCheck = document.getElementById('integrityCheck').checked;
                const domainLocking = document.getElementById('domainLocking').checked;

                // Update protection level based on settings
                let protectionLevel = 30; // Base level

                switch (selectedLevel) {
                    case 'basic':
                        protectionLevel = 40;
                        break;
                    case 'advanced':
                        protectionLevel = 70;
                        break;
                    case 'maximum':
                        protectionLevel = 95;
                        break;
                }

                // Adjust based on additional features
                if (antiDebugging) protectionLevel += 5;
                if (integrityCheck) protectionLevel += 5;
                if (domainLocking) protectionLevel += 10;
                if (!productionSafe) protectionLevel += 5; // Higher security but less compatibility

                // Cap at 100
                protectionLevel = Math.min(protectionLevel, 100);

                this.protectionLevel = protectionLevel;
                this.updateProtectionLevel();

                // Show configuration summary
                this.showConfigurationSummary();
            }

            addDomain() {
                const domainInput = document.getElementById('domainInput');
                const domainList = document.getElementById('domainList');
                const domain = domainInput.value.trim();

                if (domain && this.isValidDomain(domain)) {
                    // Check if domain already exists
                    const existingDomains = Array.from(domainList.querySelectorAll('.domain-item span')).map(span => span.textContent);
                    if (!existingDomains.includes(domain)) {
                        const domainItem = document.createElement('div');
                        domainItem.className = 'domain-item';
                        domainItem.innerHTML = `
                            <span>${domain}</span>
                            <button type="button" class="remove-domain">×</button>
                        `;
                        domainList.appendChild(domainItem);
                        domainInput.value = '';
                        this.initializeDomainListeners();
                    } else {
                        this.showNotification('Domain already added', 'warning');
                    }
                } else {
                    this.showNotification('Please enter a valid domain', 'warning');
                }
            }

            isValidDomain(domain) {
                const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])*$/;
                return domainRegex.test(domain) || domain === 'localhost';
            }

            initializeDomainListeners() {
                const removeButtons = document.querySelectorAll('.remove-domain');
                removeButtons.forEach(button => {
                    button.addEventListener('click', (e) => {
                        e.target.closest('.domain-item').remove();
                    });
                });
            }

            showConfigurationSummary() {
                const selectedLevel = document.querySelector('input[name="encryptionLevel"]:checked').value;
                const productionSafe = document.getElementById('productionSafe').checked;

                let summary = '';
                switch (selectedLevel) {
                    case 'basic':
                        summary = 'Basic encryption with production compatibility';
                        break;
                    case 'advanced':
                        summary = 'Advanced multi-layer encryption';
                        break;
                    case 'maximum':
                        summary = 'Maximum security with military-grade protection';
                        break;
                }

                if (productionSafe) {
                    summary += ' (Production-Safe)';
                }

                // Update the strength label with configuration info
                const strengthLabel = document.getElementById('strengthLabel');
                if (strengthLabel) {
                    strengthLabel.textContent = summary;
                }
            }

            getEncryptionConfiguration() {
                const selectedLevel = document.querySelector('input[name="encryptionLevel"]:checked').value;
                const productionSafe = document.getElementById('productionSafe').checked;
                const loadBalancerCompatible = document.getElementById('loadBalancerCompatible').checked;
                const antiDebugging = document.getElementById('antiDebugging').checked;
                const integrityCheck = document.getElementById('integrityCheck').checked;
                const domainLocking = document.getElementById('domainLocking').checked;

                // Get allowed domains
                const domainItems = document.querySelectorAll('.domain-item span');
                const allowedDomains = Array.from(domainItems).map(span => span.textContent);

                return {
                    level: selectedLevel,
                    productionSafe,
                    loadBalancerCompatible,
                    antiDebugging,
                    integrityCheck,
                    domainLocking,
                    allowedDomains,
                    protectionLevel: this.protectionLevel
                };
            }

            async processWithAPI() {
                try {
                    // Check if we should use custom encryption
                    const encryptionConfig = this.getEncryptionConfiguration();

                    if (encryptionConfig) {
                        await this.processWithCustomEncryption(encryptionConfig);
                    } else {
                        // Fallback to standard API
                        const uploadResult = await protectorAPI.uploadFiles(this.selectedFiles);
                        const config = this.getConfiguration();
                        const protectionResult = await protectorAPI.protectFiles(config);
                        this.displayResults(protectionResult.results);
                    }

                    this.showNotification('Files processed successfully!', 'info');
                } catch (error) {
                    throw new Error('API processing failed: ' + error.message);
                }
            }

            async processWithCustomEncryption(encryptionConfig) {
                // Use the custom encryption API
                const fileData = [];

                // Read file contents
                for (const file of this.selectedFiles) {
                    const content = await this.readFileContent(file);
                    fileData.push({
                        filename: file.name,
                        content: content,
                        size: file.size
                    });
                }

                try {
                    const response = await fetch(`${this.apiBaseUrl}/api/encrypt`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            files: fileData,
                            encryptionConfig: encryptionConfig
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.status === 'success') {
                        this.processedFiles = result.results;
                        this.showEncryptionResults(result.results);
                    } else {
                        throw new Error(result.error || 'Encryption failed');
                    }
                } catch (error) {
                    console.error('Custom encryption error:', error);
                    throw error;
                }
            }

            async readFileContent(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target.result);
                    reader.onerror = (e) => reject(e);
                    reader.readAsText(file);
                });
            }

            async processClientSide() {
                // Fallback client-side processing (basic obfuscation)
                const queueList = document.getElementById('queueList');

                for (let i = 0; i < this.selectedFiles.length; i++) {
                    const file = this.selectedFiles[i];

                    // Add queue item
                    const queueItem = this.createQueueItem(file, 'processing');
                    queueList.appendChild(queueItem);

                    try {
                        // Simulate processing
                        await this.simulateProcessing(file, queueItem);

                        // Mark as completed
                        this.updateQueueItem(queueItem, 'completed', 'Obfuscation completed');

                        this.processedFiles.push({
                            original: file,
                            processed: file, // In real implementation, this would be the processed file
                            downloadUrl: URL.createObjectURL(file) // Temporary URL
                        });

                    } catch (error) {
                        this.updateQueueItem(queueItem, 'error', 'Processing failed');
                    }
                }

                this.updateStats();
                this.showNotification('Client-side processing completed!', 'info');
            }

            createQueueItem(file, status) {
                const queueItem = document.createElement('div');
                queueItem.className = 'queue-item';
                queueItem.innerHTML = `
                    <div class="queue-icon ${status}">
                        ${status === 'processing' ? '⚡' : status === 'completed' ? '✓' : '✗'}
                    </div>
                    <div class="queue-details">
                        <div class="queue-filename">${file.name}</div>
                        <div class="queue-status">Processing...</div>
                        <div class="queue-progress">
                            <div class="queue-progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="queue-actions">
                        <!-- Actions will be added when completed -->
                    </div>
                `;
                return queueItem;
            }

            updateQueueItem(queueItem, status, statusText) {
                const icon = queueItem.querySelector('.queue-icon');
                const statusElement = queueItem.querySelector('.queue-status');
                const progressFill = queueItem.querySelector('.queue-progress-fill');
                const actions = queueItem.querySelector('.queue-actions');

                icon.className = `queue-icon ${status}`;
                icon.textContent = status === 'completed' ? '✓' : status === 'error' ? '✗' : '⚡';
                statusElement.textContent = statusText;
                progressFill.style.width = status === 'completed' ? '100%' : '0%';

                if (status === 'completed') {
                    actions.innerHTML = `
                        <button class="queue-btn download" onclick="obfuscator.downloadFile('${queueItem.dataset.fileIndex}')">
                            Download
                        </button>
                    `;
                }
            }

            async simulateProcessing(file, queueItem) {
                const progressFill = queueItem.querySelector('.queue-progress-fill');
                const statusElement = queueItem.querySelector('.queue-status');

                // Simulate processing steps
                const steps = [
                    'Analyzing file structure...',
                    'Applying obfuscation...',
                    'Encoding strings...',
                    'Injecting dead code...',
                    'Finalizing protection...'
                ];

                for (let i = 0; i < steps.length; i++) {
                    statusElement.textContent = steps[i];
                    progressFill.style.width = ((i + 1) / steps.length * 100) + '%';
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }

            getConfiguration() {
                return {
                    obfuscation: {
                        minify_code: document.getElementById('minify').checked,
                        remove_comments: document.getElementById('removeComments').checked,
                        remove_whitespace: document.getElementById('removeWhitespace').checked,
                        rename_variables: document.getElementById('renameVariables').checked,
                        string_encoding: document.getElementById('stringEncoding').checked,
                        control_flow_obfuscation: document.getElementById('controlFlow').checked,
                        dead_code_injection: document.getElementById('deadCode').checked,
                        function_splitting: document.getElementById('functionSplit').checked,
                        strength_level: parseInt(document.getElementById('strengthRange').value),
                        dead_code_percentage: parseInt(document.getElementById('deadCodeRange').value)
                    },
                    encryption: {
                        base64_encoding: document.getElementById('base64').checked,
                        aes_encryption: document.getElementById('aes').checked,
                        hex_encoding: document.getElementById('hexEncode').checked,
                        custom_key_generation: document.getElementById('customKey').checked,
                        encryption_layers: parseInt(document.getElementById('encodingRange').value)
                    },
                    security: {
                        anti_debugging: document.getElementById('antiDebug').checked,
                        integrity_checking: document.getElementById('integrityCheck').checked,
                        domain_locking: document.getElementById('domainLock').checked,
                        code_expiration: document.getElementById('expiration').checked
                    }
                };
            }

            displayResults(results) {
                const queueList = document.getElementById('queueList');
                queueList.innerHTML = '';

                results.forEach((result, index) => {
                    const queueItem = document.createElement('div');
                    queueItem.className = 'queue-item';
                    queueItem.dataset.fileIndex = index;

                    const iconClass = result.status === 'success' ? 'completed' : 'error';
                    const iconText = result.status === 'success' ? '✓' : '✗';
                    const statusText = result.status === 'success' ?
                        `Protected (${result.protection_level}% security)` :
                        `Failed: ${result.error}`;

                    queueItem.innerHTML = `
                        <div class="queue-icon ${iconClass}">${iconText}</div>
                        <div class="queue-details">
                            <div class="queue-filename">${result.filename}</div>
                            <div class="queue-status">${statusText}</div>
                            <div class="queue-progress">
                                <div class="queue-progress-fill" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="queue-actions">
                            ${result.status === 'success' ?
                                `<button class="queue-btn download" onclick="obfuscator.downloadFromAPI('${result.download_url}', '${result.filename}')">Download</button>` :
                                ''}
                        </div>
                    `;

                    queueList.appendChild(queueItem);
                });
            }

            showEncryptionResults(results) {
                const queueList = document.getElementById('queueList');
                queueList.innerHTML = '';

                results.forEach((result, index) => {
                    const queueItem = document.createElement('div');
                    queueItem.className = 'queue-item encryption-result';
                    queueItem.dataset.fileIndex = index;

                    const iconClass = result.status === 'success' ? 'completed' : 'error';
                    const iconText = result.status === 'success' ? '🔒' : '✗';
                    const statusText = result.status === 'success' ?
                        `Encrypted (${result.protection_level}% security, ${result.encryption_config.level.toUpperCase()})` :
                        `Failed: ${result.error}`;

                    queueItem.innerHTML = `
                        <div class="queue-icon ${iconClass}">${iconText}</div>
                        <div class="queue-details">
                            <div class="queue-filename">${result.filename}</div>
                            <div class="queue-status">${statusText}</div>
                            <div class="encryption-info">
                                ${result.status === 'success' ? `
                                    <div class="encryption-stats">
                                        <span>Size: ${this.formatFileSize(result.original_size)} → ${this.formatFileSize(result.encrypted_size)}</span>
                                        <span>Increase: +${result.size_increase_percent.toFixed(1)}%</span>
                                    </div>
                                    <div class="encryption-features">
                                        ${result.encryption_config.antiDebugging ? '🛡️ Anti-Debug' : ''}
                                        ${result.encryption_config.productionSafe ? '🚀 Production-Safe' : ''}
                                        ${result.encryption_config.domainLocking ? '🌐 Domain-Locked' : ''}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="queue-progress">
                                <div class="queue-progress-fill" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="queue-actions">
                            ${result.status === 'success' ?
                                `<button class="queue-btn download encrypted" onclick="obfuscator.downloadFromAPI('${result.download_url}', '${result.filename}')">🔒 Download</button>` :
                                ''}
                        </div>
                    `;

                    queueList.appendChild(queueItem);
                });

                // Show success message
                this.showNotification('🔒 Files encrypted successfully! Your code is now protected and unreadable.', 'success');
            }

            downloadFromAPI(url, filename) {
                if (typeof protectorAPI !== 'undefined') {
                    protectorAPI.downloadFile(url, filename);
                } else {
                    // Fallback download
                    const link = document.createElement('a');
                    link.href = `${this.apiBaseUrl}${url}`;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }

            downloadFile(index) {
                if (this.processedFiles[index]) {
                    const file = this.processedFiles[index];
                    const link = document.createElement('a');
                    link.href = file.downloadUrl;
                    link.download = 'protected_' + file.original.name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }

            downloadAll() {
                if (this.processedFiles.length === 0) {
                    this.showNotification('No files to download', 'warning');
                    return;
                }

                this.processedFiles.forEach((file, index) => {
                    setTimeout(() => this.downloadFile(index), index * 500);
                });

                this.showNotification('Downloading all files...', 'info');
            }
        }

        // Initialize the obfuscator when the page loads
        let obfuscator;
        document.addEventListener('DOMContentLoaded', function() {
            obfuscator = new CodeObfuscator();
        });
    </script>
</body>
</html>