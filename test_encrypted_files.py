#!/usr/bin/env python3
"""
Test Script for Encrypted Laravel Files
Verifies that all encrypted files are working correctly.
"""

import os
import subprocess
import json
from pathlib import Path

def test_php_syntax(file_path):
    """Test PHP syntax of a file."""
    try:
        result = subprocess.run(['php', '-l', file_path], 
                              capture_output=True, text=True)
        return result.returncode == 0, result.stderr
    except Exception as e:
        return False, str(e)

def get_file_info(file_path):
    """Get file information."""
    if not os.path.exists(file_path):
        return None
    
    stat = os.stat(file_path)
    return {
        'size': stat.st_size,
        'size_human': format_file_size(stat.st_size),
        'exists': True
    }

def format_file_size(size_bytes):
    """Format file size in human-readable format."""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def main():
    """Main test function."""
    print("🧪 Testing Encrypted Laravel Files")
    print("=" * 50)
    
    # Find the encrypted files directory
    encrypted_dirs = [d for d in os.listdir('.') if d.startswith('encrypted_laravel_files_')]
    
    if not encrypted_dirs:
        print("❌ No encrypted files directory found!")
        return
    
    # Use the most recent directory
    encrypted_dir = sorted(encrypted_dirs)[-1]
    print(f"📂 Testing files in: {encrypted_dir}")
    
    # Get list of encrypted files
    encrypted_files = [f for f in os.listdir(encrypted_dir) if f.endswith('.php')]
    
    if not encrypted_files:
        print("❌ No encrypted PHP files found!")
        return
    
    print(f"📋 Found {len(encrypted_files)} encrypted files")
    print()
    
    # Test each file
    results = {
        'total': len(encrypted_files),
        'passed': 0,
        'failed': 0,
        'details': []
    }
    
    for file_name in sorted(encrypted_files):
        file_path = os.path.join(encrypted_dir, file_name)
        file_info = get_file_info(file_path)
        
        print(f"🔍 Testing: {file_name}")
        print(f"   Size: {file_info['size_human']}")
        
        # Test PHP syntax
        syntax_ok, error_msg = test_php_syntax(file_path)
        
        if syntax_ok:
            print(f"   ✅ Syntax: OK")
            results['passed'] += 1
            status = 'passed'
        else:
            print(f"   ❌ Syntax: FAILED")
            print(f"   Error: {error_msg}")
            results['failed'] += 1
            status = 'failed'
        
        results['details'].append({
            'file': file_name,
            'status': status,
            'size': file_info['size'],
            'size_human': file_info['size_human'],
            'error': error_msg if not syntax_ok else None
        })
        
        print()
    
    # Summary
    print("📊 Test Summary")
    print("-" * 30)
    print(f"Total files: {results['total']}")
    print(f"Passed: {results['passed']}")
    print(f"Failed: {results['failed']}")
    print(f"Success rate: {(results['passed']/results['total']*100):.1f}%")
    
    # Save detailed results
    report_file = os.path.join(encrypted_dir, 'test_report.json')
    with open(report_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"\n📄 Detailed test report saved: {report_file}")
    
    # Load and display encryption report if available
    encryption_report_file = os.path.join(encrypted_dir, 'encryption_report.json')
    if os.path.exists(encryption_report_file):
        print(f"\n📋 Encryption Statistics:")
        with open(encryption_report_file, 'r') as f:
            encryption_data = json.load(f)
        
        print(f"   Total processing time: {encryption_data.get('processing_time', 0):.2f}s")
        print(f"   Successful encryptions: {encryption_data.get('successful', 0)}")
        print(f"   Failed encryptions: {encryption_data.get('failed', 0)}")
        
        # Show size increases
        print(f"\n📈 Size Increases:")
        for result in encryption_data.get('results', []):
            if result.get('status') == 'success':
                file_name = os.path.basename(result['file_path'])
                size_increase = result.get('stats', {}).get('size_increase_percent', 0)
                method = result.get('encryption_method', 'unknown')
                print(f"   • {file_name}: +{size_increase:.1f}% ({method})")
    
    if results['failed'] == 0:
        print(f"\n🎉 All {results['total']} encrypted files are working correctly!")
    else:
        print(f"\n⚠️  {results['failed']} files have issues that need attention.")

if __name__ == '__main__':
    main()
