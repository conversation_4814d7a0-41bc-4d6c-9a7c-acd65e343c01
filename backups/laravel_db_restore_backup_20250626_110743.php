<?php
/**
 * Laravel Database Restore Tool for cPanel Shared Hosting
 * 
 * This script allows you to delete all tables from your database
 * and restore them from an SQL dump file on shared hosting
 * where terminal access is limited.
 * 
 * ⚠️ SECURITY WARNING: This script is extremely dangerous as it allows
 * deleting all database tables. Delete immediately after use.
 * 
 * Usage: Upload this script to your public/scripts directory and access it via browser.
 */

// Set execution time limit to 600 seconds (10 minutes) as imports can take time
set_time_limit(600);
ini_set('memory_limit', '512M'); // Increase memory limit for large imports

// Start output buffering
ob_start();

// Define the base directory (Laravel root)
$baseDir = dirname(dirname(__DIR__));

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Database Restore Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2d3748;
        }
        .success {
            color: #38a169;
            background-color: #f0fff4;
            border-left: 4px solid #38a169;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .warning {
            color: #d69e2e;
            background-color: #fffaf0;
            border-left: 4px solid #d69e2e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .error {
            color: #e53e3e;
            background-color: #fff5f5;
            border-left: 4px solid #e53e3e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .info {
            color: #3182ce;
            background-color: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 8px 12px;
            margin: 8px 0;
        }
        code {
            background-color: #f7fafc;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
        }
        pre {
            background-color: #f7fafc;
            padding: 12px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .command-output {
            background-color: #1a202c;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 5px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f7fafc;
        }
        tr:nth-child(even) {
            background-color: #f7fafc;
        }
        .security-warning {
            background-color: #742a2a;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .progress-container {
            width: 100%;
            background-color: #e2e8f0;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            background-color: #4299e1;
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s;
        }
        button, .button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
            text-decoration: none;
            display: inline-block;
        }
        button:hover, .button:hover {
            background-color: #3182ce;
        }
        .danger-button {
            background-color: #e53e3e;
        }
        .danger-button:hover {
            background-color: #c53030;
        }
        input[type="text"], input[type="password"], input[type="file"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .checkbox-container {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="security-warning">
        ⚠️ EXTREME SECURITY WARNING: This script can delete ALL tables in your database!
        Delete this file immediately after use!
    </div>
    <h1>Laravel Database Restore Tool</h1>
';

// Function to get database connection details from Laravel .env file
function getDatabaseCredentials($baseDir) {
    $envFile = $baseDir . '/.env';
    $credentials = [
        'host' => 'localhost',
        'database' => '',
        'username' => '',
        'password' => '',
        'port' => '3306',
    ];
    
    if (file_exists($envFile)) {
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                switch ($key) {
                    case 'DB_HOST':
                        $credentials['host'] = $value;
                        break;
                    case 'DB_DATABASE':
                        $credentials['database'] = $value;
                        break;
                    case 'DB_USERNAME':
                        $credentials['username'] = $value;
                        break;
                    case 'DB_PASSWORD':
                        $credentials['password'] = $value;
                        break;
                    case 'DB_PORT':
                        $credentials['port'] = $value;
                        break;
                }
            }
        }
    }
    
    return $credentials;
}

// Function to test database connection
function testDatabaseConnection($credentials) {
    try {
        $dsn = "mysql:host={$credentials['host']};dbname={$credentials['database']};port={$credentials['port']}";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, $credentials['username'], $credentials['password'], $options);
        return [true, $pdo, "Connected successfully to database: {$credentials['database']}"];
    } catch (PDOException $e) {
        return [false, null, "Connection failed: " . $e->getMessage()];
    }
}

// Function to get all tables in the database
function getAllTables($pdo) {
    $tables = [];
    $result = $pdo->query("SHOW TABLES");
    while ($row = $result->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    return $tables;
}

// Function to drop all tables
function dropAllTables($pdo) {
    $tables = getAllTables($pdo);
    
    if (empty($tables)) {
        return [true, "No tables found to drop."];
    }
    
    try {
        // Disable foreign key checks temporarily
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        $dropped = 0;
        $errors = [];
        
        foreach ($tables as $table) {
            try {
                $pdo->exec("DROP TABLE `$table`");
                $dropped++;
            } catch (PDOException $e) {
                $errors[] = "Failed to drop table '$table': " . $e->getMessage();
            }
        }
        
        // Re-enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        if (empty($errors)) {
            return [true, "Successfully dropped all $dropped tables."];
        } else {
            return [false, "Dropped $dropped tables, but encountered " . count($errors) . " errors: " . implode("; ", $errors)];
        }
    } catch (PDOException $e) {
        return [false, "Error dropping tables: " . $e->getMessage()];
    }
}

// Function to import SQL file
function importSqlFile($pdo, $filePath) {
    try {
        // Read the SQL file
        $sql = file_get_contents($filePath);
        if ($sql === false) {
            return [false, "Failed to read SQL file."];
        }
        
        // Split SQL file into individual statements
        $statements = [];
        $delimiter = ';';
        $sql = trim($sql);
        
        // Handle delimiter changes (e.g., DELIMITER $$ in procedures/functions)
        $lines = explode("\n", $sql);
        $statement = '';
        $inDelimiterChange = false;
        $currentDelimiter = ';';
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            
            // Skip comments and empty lines
            if (empty($trimmedLine) || strpos($trimmedLine, '--') === 0 || strpos($trimmedLine, '#') === 0) {
                continue;
            }
            
            // Check for DELIMITER command
            if (preg_match('/^DELIMITER\s+(.+)$/i', $trimmedLine, $matches)) {
                if ($statement !== '') {
                    $statements[] = $statement;
                    $statement = '';
                }
                $currentDelimiter = $matches[1];
                continue;
            }
            
            // Add the line to the current statement
            $statement .= $line . "\n";
            
            // Check if the line ends with the current delimiter
            if (preg_match('/' . preg_quote($currentDelimiter, '/') . '\s*$/', $trimmedLine)) {
                // Remove the delimiter from the end
                $statement = preg_replace('/' . preg_quote($currentDelimiter, '/') . '\s*$/', '', $statement);
                $statements[] = $statement;
                $statement = '';
            }
        }
        
        // Add the last statement if it's not empty
        if (!empty(trim($statement))) {
            $statements[] = $statement;
        }
        
        // Execute each statement
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        
        $executed = 0;
        $errors = [];
        
        foreach ($statements as $statement) {
            if (trim($statement) === '') {
                continue;
            }
            
            try {
                $pdo->exec($statement);
                $executed++;
            } catch (PDOException $e) {
                $errors[] = "Error executing SQL: " . $e->getMessage() . " in statement: " . substr($statement, 0, 100) . "...";
            }
        }
        
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        if (empty($errors)) {
            return [true, "Successfully executed $executed SQL statements."];
        } else {
            return [false, "Executed $executed statements, but encountered " . count($errors) . " errors: " . implode("<br>", $errors)];
        }
    } catch (Exception $e) {
        return [false, "Error importing SQL file: " . $e->getMessage()];
    }
}

// Get database credentials from .env
$credentials = getDatabaseCredentials($baseDir);

// Check if database credentials are available
if (empty($credentials['database']) || empty($credentials['username'])) {
    echo '<div class="error">
        <h2>Database Configuration Error</h2>
        <p>Could not find database credentials in your .env file.</p>
        <p>Please make sure your .env file exists and contains DB_DATABASE, DB_USERNAME, and DB_PASSWORD.</p>
    </div>';
    exit;
}

// Display database information
echo '<div class="info">
    <h2>Database Information</h2>
    <p><strong>Host:</strong> ' . htmlspecialchars($credentials['host']) . '</p>
    <p><strong>Database:</strong> ' . htmlspecialchars($credentials['database']) . '</p>
    <p><strong>Username:</strong> ' . htmlspecialchars($credentials['username']) . '</p>
</div>';

// Test database connection
list($connected, $pdo, $connectionMessage) = testDatabaseConnection($credentials);

if ($connected) {
    echo '<div class="success">' . htmlspecialchars($connectionMessage) . '</div>';
    
    // Get all tables
    $tables = getAllTables($pdo);
    $tableCount = count($tables);
    
    echo '<div class="info">
        <h2>Current Database Tables</h2>
        <p>Found ' . $tableCount . ' tables in the database.</p>';
    
    if ($tableCount > 0) {
        echo '<ul>';
        foreach ($tables as $table) {
            echo '<li>' . htmlspecialchars($table) . '</li>';
        }
        echo '</ul>';
    }
    
    echo '</div>';
    
    // Process form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Handle table deletion
        if (isset($_POST['action']) && $_POST['action'] === 'drop-tables') {
            // Verify confirmation code
            $confirmationCode = $_POST['confirmation_code'] ?? '';
            if ($confirmationCode !== 'DELETE-ALL-TABLES') {
                echo '<div class="error">Incorrect confirmation code. Tables were not deleted.</div>';
            } else {
                echo '<h2>Dropping All Tables</h2>';
                echo '<div class="command-output">';
                
                list($success, $message) = dropAllTables($pdo);
                
                if ($success) {
                    echo '<div class="success">' . htmlspecialchars($message) . '</div>';
                } else {
                    echo '<div class="error">' . htmlspecialchars($message) . '</div>';
                }
                
                echo '</div>';
                
                // Refresh table list
                $tables = getAllTables($pdo);
                $tableCount = count($tables);
                
                echo '<div class="info">
                    <h3>Updated Database Tables</h3>
                    <p>Found ' . $tableCount . ' tables in the database.</p>';
                
                if ($tableCount > 0) {
                    echo '<ul>';
                    foreach ($tables as $table) {
                        echo '<li>' . htmlspecialchars($table) . '</li>';
                    }
                    echo '</ul>';
                }
                
                echo '</div>';
            }
        }
        
        // Handle SQL file import
        if (isset($_POST['action']) && $_POST['action'] === 'import-sql') {
            echo '<h2>Importing SQL File</h2>';
            echo '<div class="command-output">';
            
            // Check if file was uploaded
            if (!isset($_FILES['sql_file']) || $_FILES['sql_file']['error'] !== UPLOAD_ERR_OK) {
                echo '<div class="error">Error uploading file: ';
                
                switch ($_FILES['sql_file']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                        echo 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        echo 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form.';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        echo 'The uploaded file was only partially uploaded.';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        echo 'No file was uploaded.';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        echo 'Missing a temporary folder.';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        echo 'Failed to write file to disk.';
                        break;
                    case UPLOAD_ERR_EXTENSION:
                        echo 'A PHP extension stopped the file upload.';
                        break;
                    default:
                        echo 'Unknown error.';
                        break;
                }
                
                echo '</div>';
            } else {
                // Check file type
                $fileName = $_FILES['sql_file']['name'];
                $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                
                if ($fileExt !== 'sql') {
                    echo '<div class="error">Invalid file type. Only SQL files are allowed.</div>';
                } else {
                    $tmpFilePath = $_FILES['sql_file']['tmp_name'];
                    
                    echo '<div class="info">Importing SQL file: ' . htmlspecialchars($fileName) . '</div>';
                    
                    // Import the SQL file
                    list($success, $message) = importSqlFile($pdo, $tmpFilePath);
                    
                    if ($success) {
                        echo '<div class="success">' . htmlspecialchars($message) . '</div>';
                    } else {
                        echo '<div class="error">' . $message . '</div>';
                    }
                    
                    // Refresh table list
                    $tables = getAllTables($pdo);
                    $tableCount = count($tables);
                    
                    echo '<div class="info">
                        <h3>Updated Database Tables</h3>
                        <p>Found ' . $tableCount . ' tables in the database.</p>';
                    
                    if ($tableCount > 0) {
                        echo '<ul>';
                        foreach ($tables as $table) {
                            echo '<li>' . htmlspecialchars($table) . '</li>';
                        }
                        echo '</ul>';
                    }
                    
                    echo '</div>';
                }
            }
            
            echo '</div>';
        }
        
        // Handle combined action (drop tables and import SQL)
        if (isset($_POST['action']) && $_POST['action'] === 'drop-and-import') {
            // Verify confirmation code
            $confirmationCode = $_POST['confirmation_code'] ?? '';
            if ($confirmationCode !== 'DELETE-AND-RESTORE') {
                echo '<div class="error">Incorrect confirmation code. Operation aborted.</div>';
            } else {
                echo '<h2>Drop Tables and Import SQL</h2>';
                echo '<div class="command-output">';
                
                // Step 1: Drop all tables
                echo '<h3>Step 1: Dropping All Tables</h3>';
                list($dropSuccess, $dropMessage) = dropAllTables($pdo);
                
                if ($dropSuccess) {
                    echo '<div class="success">' . htmlspecialchars($dropMessage) . '</div>';
                } else {
                    echo '<div class="error">' . htmlspecialchars($dropMessage) . '</div>';
                }
                
                // Step 2: Import SQL file
                echo '<h3>Step 2: Importing SQL File</h3>';
                
                // Check if file was uploaded
                if (!isset($_FILES['sql_file']) || $_FILES['sql_file']['error'] !== UPLOAD_ERR_OK) {
                    echo '<div class="error">Error uploading file: ';
                    
                    switch ($_FILES['sql_file']['error']) {
                        case UPLOAD_ERR_INI_SIZE:
                            echo 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            echo 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form.';
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            echo 'The uploaded file was only partially uploaded.';
                            break;
                        case UPLOAD_ERR_NO_FILE:
                            echo 'No file was uploaded.';
                            break;
                        case UPLOAD_ERR_NO_TMP_DIR:
                            echo 'Missing a temporary folder.';
                            break;
                        case UPLOAD_ERR_CANT_WRITE:
                            echo 'Failed to write file to disk.';
                            break;
                        case UPLOAD_ERR_EXTENSION:
                            echo 'A PHP extension stopped the file upload.';
                            break;
                        default:
                            echo 'Unknown error.';
                            break;
                    }
                    
                    echo '</div>';
                } else {
                    // Check file type
                    $fileName = $_FILES['sql_file']['name'];
                    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                    
                    if ($fileExt !== 'sql') {
                        echo '<div class="error">Invalid file type. Only SQL files are allowed.</div>';
                    } else {
                        $tmpFilePath = $_FILES['sql_file']['tmp_name'];
                        
                        echo '<div class="info">Importing SQL file: ' . htmlspecialchars($fileName) . '</div>';
                        
                        // Import the SQL file
                        list($importSuccess, $importMessage) = importSqlFile($pdo, $tmpFilePath);
                        
                        if ($importSuccess) {
                            echo '<div class="success">' . htmlspecialchars($importMessage) . '</div>';
                        } else {
                            echo '<div class="error">' . $importMessage . '</div>';
                        }
                    }
                }
                
                echo '</div>';
                
                // Refresh table list
                $tables = getAllTables($pdo);
                $tableCount = count($tables);
                
                echo '<div class="info">
                    <h3>Updated Database Tables</h3>
                    <p>Found ' . $tableCount . ' tables in the database.</p>';
                
                if ($tableCount > 0) {
                    echo '<ul>';
                    foreach ($tables as $table) {
                        echo '<li>' . htmlspecialchars($table) . '</li>';
                    }
                    echo '</ul>';
                }
                
                echo '</div>';
            }
        }
    }
    
    // Display forms for database operations
    echo '<h2>Database Operations</h2>';
    
    // Drop all tables form
    echo '<div class="form-group" style="background-color: #fff5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>Drop All Tables</h3>
        <p class="warning">⚠️ This will delete ALL tables in your database. This action cannot be undone!</p>
        <form method="post" onsubmit="return confirm(\'WARNING: You are about to delete ALL tables in your database. This action CANNOT be undone! Are you sure you want to continue?\')">
            <input type="hidden" name="action" value="drop-tables">
            <div class="form-group">
                <label for="confirmation_code">Type "DELETE-ALL-TABLES" to confirm:</label>
                <input type="text" id="confirmation_code" name="confirmation_code" required>
            </div>
            <button type="submit" class="danger-button">Drop All Tables</button>
        </form>
    </div>';
    
    // Import SQL file form
    echo '<div class="form-group" style="background-color: #f0fff4; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>Import SQL File</h3>
        <form method="post" enctype="multipart/form-data">
            <input type="hidden" name="action" value="import-sql">
            <div class="form-group">
                <label for="sql_file">Select SQL File:</label>
                <input type="file" id="sql_file" name="sql_file" accept=".sql" required>
            </div>
            <button type="submit">Import SQL File</button>
        </form>
    </div>';
    
    // Combined operation: Drop tables and import SQL
    echo '<div class="form-group" style="background-color: #fffaf0; padding: 15px; border-radius: 5px;">
        <h3>Drop All Tables and Import SQL File</h3>
        <p class="warning">⚠️ This will delete ALL tables in your database and then import the selected SQL file.</p>
        <form method="post" enctype="multipart/form-data" onsubmit="return confirm(\'WARNING: You are about to delete ALL tables in your database and import a new SQL file. This action CANNOT be undone! Are you sure you want to continue?\')">
            <input type="hidden" name="action" value="drop-and-import">
            <div class="form-group">
                <label for="sql_file_combined">Select SQL File:</label>
                <input type="file" id="sql_file_combined" name="sql_file" accept=".sql" required>
            </div>
            <div class="form-group">
                <label for="confirmation_code_combined">Type "DELETE-AND-RESTORE" to confirm:</label>
                <input type="text" id="confirmation_code_combined" name="confirmation_code" required>
            </div>
            <button type="submit" class="danger-button">Drop Tables and Import SQL</button>
        </form>
    </div>';
} else {
    echo '<div class="error">' . htmlspecialchars($connectionMessage) . '</div>';
}

echo '
<div class="security-warning" style="margin-top: 30px;">
    ⚠️ SECURITY REMINDER: Delete this file immediately after use!
</div>

</body>
</html>'; 