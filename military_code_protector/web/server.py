"""
Web Server for Military-Grade Code Protection
Provides HTTP API for the HTML interface integration.
"""

import os
import json
import tempfile
from flask import Flask, request, jsonify, send_file, render_template_string
from flask_cors import CORS
from werkzeug.utils import secure_filename
from typing import Dict, Any, List

class WebServer:
    """Web server for the protection system API."""
    
    def __init__(self, protector):
        """
        Initialize the web server.
        
        Args:
            protector: MilitaryCodeProtector instance
        """
        self.protector = protector
        self.app = Flask(__name__)
        self.app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
        self.temp_dir = tempfile.mkdtemp()

        # Enable CORS for all routes
        CORS(self.app)

        # Setup routes
        self._setup_routes()
        
    def _setup_routes(self):
        """Setup Flask routes for the API."""
        
        @self.app.route('/')
        def index():
            """Serve the main HTML interface."""
            # Read the existing HTML obfuscator file
            html_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                   'laravel_code_obfuscator.html')

            if os.path.exists(html_file):
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Inject API integration script
                api_script = self._get_api_integration_script()
                content = content.replace('</body>', f'{api_script}\n</body>')

                return content
            else:
                return self._get_default_interface()
                
        @self.app.route('/api/upload', methods=['POST'])
        def upload_files():
            """Handle file uploads."""
            try:
                if 'files' not in request.files:
                    return jsonify({'error': 'No files provided'}), 400
                    
                files = request.files.getlist('files')
                uploaded_files = []
                
                for file in files:
                    if file.filename == '':
                        continue
                        
                    filename = secure_filename(file.filename)
                    file_path = os.path.join(self.temp_dir, filename)
                    file.save(file_path)
                    
                    # Validate file
                    validation = self.protector.file_manager.validate_file(file_path)
                    
                    uploaded_files.append({
                        'filename': filename,
                        'path': file_path,
                        'size': validation['file_size'],
                        'valid': validation['valid'],
                        'errors': validation['errors']
                    })
                    
                return jsonify({
                    'status': 'success',
                    'files': uploaded_files
                })
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
                
        @self.app.route('/api/protect', methods=['POST'])
        def protect_files():
            """Handle file protection requests."""
            try:
                data = request.get_json()

                if not data or 'files' not in data:
                    return jsonify({'error': 'No files specified'}), 400

                files_to_protect = data['files']
                config_overrides = data.get('config', {})

                results = []

                for file_info in files_to_protect:
                    # Handle both file path (from upload) and direct content scenarios
                    if 'path' in file_info:
                        # File was uploaded via /api/upload
                        file_path = file_info['path']

                        if not os.path.exists(file_path):
                            results.append({
                                'filename': file_info['filename'],
                                'status': 'error',
                                'error': 'File not found'
                            })
                            continue

                        # Generate output path
                        output_path = os.path.join(self.temp_dir,
                                                 f"protected_{file_info['filename']}")

                        # Protect the file
                        result = self.protector.protect_file(file_path, output_path, config_overrides)

                    elif 'content' in file_info:
                        # Direct content from web interface
                        filename = file_info['filename']
                        content = file_info['content']

                        # Create temporary input file
                        temp_input_path = os.path.join(self.temp_dir, f"temp_input_{filename}")
                        with open(temp_input_path, 'w', encoding='utf-8') as f:
                            f.write(content)

                        # Generate output path
                        output_path = os.path.join(self.temp_dir, f"protected_{filename}")

                        # Protect the file
                        result = self.protector.protect_file(temp_input_path, output_path, config_overrides)

                        # Clean up temporary input file
                        try:
                            os.remove(temp_input_path)
                        except:
                            pass  # Ignore cleanup errors

                    else:
                        results.append({
                            'filename': file_info.get('filename', 'unknown'),
                            'status': 'error',
                            'error': 'No file path or content provided'
                        })
                        continue

                    if result['status'] == 'success':
                        results.append({
                            'filename': file_info['filename'],
                            'status': 'success',
                            'protected_path': output_path,
                            'original_size': result['original_size'],
                            'protected_size': result['protected_size'],
                            'protection_level': result['protection_level'],
                            'download_url': f'/api/download/{os.path.basename(output_path)}'
                        })
                    else:
                        results.append({
                            'filename': file_info['filename'],
                            'status': 'error',
                            'error': result.get('error', 'Unknown error')
                        })

                return jsonify({
                    'status': 'success',
                    'results': results
                })

            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/encrypt', methods=['POST'])
        def encrypt_files():
            """Handle custom encryption requests."""
            try:
                data = request.get_json()

                if not data or 'files' not in data:
                    return jsonify({'error': 'No files provided'}), 400

                files_to_encrypt = data['files']
                encryption_config = data.get('encryptionConfig', {})

                results = []

                for file_info in files_to_encrypt:
                    # Handle both file path (from upload) and direct content scenarios
                    if 'path' in file_info:
                        # File was uploaded via /api/upload
                        file_path = file_info['path']

                        if not os.path.exists(file_path):
                            results.append({
                                'filename': file_info['filename'],
                                'status': 'error',
                                'error': 'File not found'
                            })
                            continue

                        # Generate output path
                        output_path = os.path.join(self.temp_dir,
                                                 f"encrypted_{file_info['filename']}")

                        # Encrypt the file using custom encryption
                        result = self.protector.create_custom_encrypted_file(
                            file_path, output_path, encryption_config
                        )

                    elif 'content' in file_info:
                        # Direct content from web interface
                        filename = file_info['filename']
                        content = file_info['content']

                        # Create temporary input file
                        temp_input_path = os.path.join(self.temp_dir, f"temp_input_{filename}")
                        with open(temp_input_path, 'w', encoding='utf-8') as f:
                            f.write(content)

                        # Generate output path
                        output_path = os.path.join(self.temp_dir, f"encrypted_{filename}")

                        # Encrypt the file using custom encryption
                        result = self.protector.create_custom_encrypted_file(
                            temp_input_path, output_path, encryption_config
                        )

                        # Clean up temporary input file
                        try:
                            os.remove(temp_input_path)
                        except:
                            pass  # Ignore cleanup errors

                    else:
                        results.append({
                            'filename': file_info.get('filename', 'unknown'),
                            'status': 'error',
                            'error': 'No file path or content provided'
                        })
                        continue

                    if result['status'] == 'success':
                        results.append({
                            'filename': file_info['filename'],
                            'status': 'success',
                            'encrypted_path': output_path,
                            'original_size': result['original_size'],
                            'encrypted_size': result['encrypted_size'],
                            'protection_level': result['protection_level'],
                            'encryption_config': result['encryption_config'],
                            'size_increase_percent': result['size_increase_percent'],
                            'download_url': f'/api/download/{os.path.basename(output_path)}'
                        })
                    else:
                        results.append({
                            'filename': file_info['filename'],
                            'status': 'error',
                            'error': result.get('error', 'Unknown error')
                        })

                return jsonify({
                    'status': 'success',
                    'results': results
                })

            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/api/download/<filename>')
        def download_file(filename):
            """Handle file downloads."""
            try:
                file_path = os.path.join(self.temp_dir, secure_filename(filename))
                
                if not os.path.exists(file_path):
                    return jsonify({'error': 'File not found'}), 404
                    
                return send_file(file_path, as_attachment=True, 
                               download_name=filename)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
                
        @self.app.route('/api/config', methods=['GET', 'POST'])
        def handle_config():
            """Handle configuration requests."""
            try:
                if request.method == 'GET':
                    # Return current configuration
                    return jsonify({
                        'status': 'success',
                        'config': self.protector.config.to_dict(),
                        'protection_level': self.protector.config.get_protection_level()
                    })
                    
                elif request.method == 'POST':
                    # Update configuration
                    data = request.get_json()
                    
                    if not data:
                        return jsonify({'error': 'No configuration data provided'}), 400
                        
                    self.protector.config.update(data)
                    
                    return jsonify({
                        'status': 'success',
                        'config': self.protector.config.to_dict(),
                        'protection_level': self.protector.config.get_protection_level()
                    })
                    
            except Exception as e:
                return jsonify({'error': str(e)}), 500
                
        @self.app.route('/api/status')
        def get_status():
            """Get system status."""
            return jsonify({
                'status': 'online',
                'version': '1.0.0',
                'supported_extensions': self.protector.config.get_file_extensions(),
                'temp_dir': self.temp_dir
            })
            
    def _get_api_integration_script(self) -> str:
        """Generate JavaScript for API integration."""
        return """
<script>
// API Integration for Military Code Protector
class ProtectorAPI {
    constructor() {
        this.baseUrl = '';
        this.uploadedFiles = [];
    }
    
    async uploadFiles(files) {
        const formData = new FormData();
        
        for (let file of files) {
            formData.append('files', file);
        }
        
        try {
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                this.uploadedFiles = result.files;
                return result;
            } else {
                throw new Error(result.error || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }
    
    async protectFiles(config = {}) {
        try {
            const response = await fetch('/api/protect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    files: this.uploadedFiles.filter(f => f.valid),
                    config: config
                })
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                return result;
            } else {
                throw new Error(result.error || 'Protection failed');
            }
        } catch (error) {
            console.error('Protection error:', error);
            throw error;
        }
    }
    
    async updateConfig(config) {
        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
            
            const result = await response.json();
            
            if (result.status === 'success') {
                return result;
            } else {
                throw new Error(result.error || 'Config update failed');
            }
        } catch (error) {
            console.error('Config error:', error);
            throw error;
        }
    }
    
    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Initialize API
const protectorAPI = new ProtectorAPI();

// Override existing functionality to use API
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const startButton = document.getElementById('startObfuscation');
    const uploadZone = document.getElementById('uploadZone');
    
    if (fileInput) {
        fileInput.addEventListener('change', async function(e) {
            const files = Array.from(e.target.files);
            
            if (files.length > 0) {
                try {
                    showLoading('Uploading files...');
                    const result = await protectorAPI.uploadFiles(files);
                    hideLoading();
                    
                    updateFileList(result.files);
                    updateStats(result.files);
                    
                    if (startButton) {
                        startButton.disabled = false;
                    }
                } catch (error) {
                    hideLoading();
                    alert('Upload failed: ' + error.message);
                }
            }
        });
    }
    
    if (startButton) {
        startButton.addEventListener('click', async function() {
            try {
                showLoading('Protecting files...');
                
                // Collect configuration from form
                const config = collectConfiguration();
                
                const result = await protectorAPI.protectFiles(config);
                hideLoading();
                
                displayResults(result.results);
                
            } catch (error) {
                hideLoading();
                alert('Protection failed: ' + error.message);
            }
        });
    }
});

function collectConfiguration() {
    const config = {};
    
    // Collect obfuscation settings
    config.obfuscation = {
        minify_code: document.getElementById('minify')?.checked || false,
        remove_comments: document.getElementById('removeComments')?.checked || false,
        remove_whitespace: document.getElementById('removeWhitespace')?.checked || false,
        rename_variables: document.getElementById('renameVariables')?.checked || false,
        string_encoding: document.getElementById('stringEncoding')?.checked || false,
        control_flow_obfuscation: document.getElementById('controlFlow')?.checked || false,
        dead_code_injection: document.getElementById('deadCode')?.checked || false,
        function_splitting: document.getElementById('functionSplit')?.checked || false,
        strength_level: parseInt(document.getElementById('strengthRange')?.value || 7)
    };
    
    // Collect encryption settings
    config.encryption = {
        base64_encoding: document.getElementById('base64')?.checked || false,
        hex_encoding: document.getElementById('hexEncode')?.checked || false,
        aes_encryption: document.getElementById('aes')?.checked || false,
        custom_key_generation: document.getElementById('customKey')?.checked || false
    };
    
    // Collect security settings
    config.security = {
        anti_debugging: document.getElementById('antiDebug')?.checked || false,
        integrity_checking: document.getElementById('integrityCheck')?.checked || false,
        domain_locking: document.getElementById('domainLock')?.checked || false,
        code_expiration: document.getElementById('expiration')?.checked || false
    };
    
    return config;
}

function updateFileList(files) {
    const fileList = document.getElementById('fileList');
    const filesCount = document.getElementById('filesCount');
    const totalSize = document.getElementById('totalSize');
    
    if (fileList) {
        fileList.style.display = files.length > 0 ? 'block' : 'none';
        fileList.innerHTML = '';
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-icon">${file.filename.split('.').pop().toUpperCase()}</div>
                    <div class="file-details">
                        <div class="file-name">${file.filename}</div>
                        <div class="file-size">${formatFileSize(file.size)}</div>
                    </div>
                </div>
                ${file.valid ? '' : '<span style="color: red;">Invalid</span>'}
            `;
            fileList.appendChild(fileItem);
        });
    }
    
    if (filesCount) {
        filesCount.textContent = files.length;
    }
    
    if (totalSize) {
        const total = files.reduce((sum, file) => sum + file.size, 0);
        totalSize.textContent = formatFileSize(total);
    }
}

function updateStats(files) {
    // Update statistics display
    const validFiles = files.filter(f => f.valid);
    document.getElementById('filesCount').textContent = validFiles.length;
}

function displayResults(results) {
    // Display protection results
    const queueList = document.getElementById('queueList');
    const processingQueue = document.getElementById('processingQueue');
    
    if (queueList && processingQueue) {
        processingQueue.style.display = 'block';
        queueList.innerHTML = '';
        
        results.forEach(result => {
            const queueItem = document.createElement('div');
            queueItem.className = 'queue-item';
            
            const iconClass = result.status === 'success' ? 'completed' : 'error';
            const statusText = result.status === 'success' ? 
                `Protected (${result.protection_level}% security)` : 
                `Failed: ${result.error}`;
            
            queueItem.innerHTML = `
                <div class="queue-icon ${iconClass}">
                    ${result.status === 'success' ? '✓' : '✗'}
                </div>
                <div class="queue-details">
                    <div class="queue-filename">${result.filename}</div>
                    <div class="queue-status">${statusText}</div>
                </div>
                <div class="queue-actions">
                    ${result.status === 'success' ? 
                        `<button class="queue-btn download" onclick="protectorAPI.downloadFile('${result.download_url}', '${result.filename}')">Download</button>` : 
                        ''}
                </div>
            `;
            
            queueList.appendChild(queueItem);
        });
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function showLoading(message) {
    const overlay = document.getElementById('loadingOverlay');
    const text = document.querySelector('.loading-text');
    if (overlay) {
        overlay.style.display = 'flex';
        if (text) text.textContent = message;
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}
</script>
"""
        
    def _get_default_interface(self) -> str:
        """Get default HTML interface if file not found."""
        return """
<!DOCTYPE html>
<html>
<head>
    <title>Military Code Protector</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .upload-zone { border: 2px dashed #ccc; padding: 40px; text-align: center; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Military-Grade Code Protector</h1>
        <div class="upload-zone">
            <input type="file" id="fileInput" multiple>
            <p>Select files to protect</p>
        </div>
        <button id="startObfuscation" class="btn" disabled>Start Protection</button>
        <div id="results"></div>
    </div>
</body>
</html>
"""
        
    def run(self, host='localhost', port=8080, debug=False):
        """Run the web server."""
        self.app.run(host=host, port=port, debug=debug)
