#!/usr/bin/env python3
"""
Military-Grade Code Protection Tool
Copyright (c) 2024. All rights reserved.

Main entry point for the code protection system.
Provides command-line interface and web server capabilities.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.obfuscator import MilitaryObfuscator
from core.config import ProtectionConfig
from core.custom_encryption import CustomEncryptionEngine
from core.enhanced_encryption import EnhancedEncryptionEngine
from utils.file_manager import FileManager
try:
    from web.server import WebServer
except ImportError:
    WebServer = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('protection.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class MilitaryCodeProtector:
    """Main class for the military-grade code protection system."""
    
    def __init__(self):
        self.config = ProtectionConfig()
        self.obfuscator = MilitaryObfuscator(self.config)
        self.custom_encryption = CustomEncryptionEngine()
        self.enhanced_encryption = EnhancedEncryptionEngine()
        self.file_manager = FileManager()
        self.web_server = WebServer(self) if WebServer else None

    def protect_file(self, input_path, output_path=None, config_overrides=None):
        """
        Protect a single file with military-grade obfuscation.
        
        Args:
            input_path (str): Path to the input file
            output_path (str): Path for the protected output file
            config_overrides (dict): Configuration overrides
            
        Returns:
            dict: Protection results and statistics
        """
        try:
            logger.info(f"Starting protection of file: {input_path}")
            
            # Load and validate input file
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"Input file not found: {input_path}")
                
            # Create backup (only for non-temporary files)
            backup_path = None
            try:
                # Skip backup for temporary files (they start with temp_)
                if not os.path.basename(input_path).startswith('temp_'):
                    backup_path = self.file_manager.create_backup(input_path)
                    logger.info(f"Backup created: {backup_path}")
                else:
                    logger.info("Skipping backup for temporary file")
            except Exception as backup_error:
                logger.warning(f"Backup creation failed: {backup_error}")
                # Continue without backup for temporary files
            
            # Apply configuration overrides
            if config_overrides:
                self.config.update(config_overrides)
                
            # Determine output path
            if not output_path:
                output_path = self._generate_output_path(input_path)
                
            # Perform obfuscation
            result = self.obfuscator.protect_file(input_path, output_path)
            
            logger.info(f"Protection completed: {output_path}")
            return result
            
        except Exception as e:
            logger.error(f"Protection failed: {str(e)}")
            raise
            
    def protect_directory(self, input_dir, output_dir=None, file_patterns=None):
        """
        Protect all files in a directory.
        
        Args:
            input_dir (str): Input directory path
            output_dir (str): Output directory path
            file_patterns (list): File patterns to include
            
        Returns:
            dict: Batch protection results
        """
        try:
            logger.info(f"Starting batch protection of directory: {input_dir}")
            
            if not os.path.exists(input_dir):
                raise FileNotFoundError(f"Input directory not found: {input_dir}")
                
            # Get files to process
            files_to_process = self.file_manager.get_files_to_process(
                input_dir, file_patterns or ['*.html', '*.css', '*.js', '*.php']
            )
            
            results = {
                'total_files': len(files_to_process),
                'processed': 0,
                'failed': 0,
                'results': []
            }
            
            for file_path in files_to_process:
                try:
                    relative_path = os.path.relpath(file_path, input_dir)
                    if output_dir:
                        output_path = os.path.join(output_dir, relative_path)
                        os.makedirs(os.path.dirname(output_path), exist_ok=True)
                    else:
                        output_path = None
                        
                    result = self.protect_file(file_path, output_path)
                    results['results'].append({
                        'file': relative_path,
                        'status': 'success',
                        'result': result
                    })
                    results['processed'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to process {file_path}: {str(e)}")
                    results['results'].append({
                        'file': relative_path,
                        'status': 'failed',
                        'error': str(e)
                    })
                    results['failed'] += 1
                    
            logger.info(f"Batch protection completed. Processed: {results['processed']}, Failed: {results['failed']}")
            return results

        except Exception as e:
            logger.error(f"Batch protection failed: {str(e)}")
            raise

    def create_custom_encrypted_file(self, input_path, output_path=None, encryption_config=None):
        """
        Create a custom encrypted file using the new encryption engine.

        Args:
            input_path (str): Path to the input file
            output_path (str): Path for the encrypted output file
            encryption_config (dict): Encryption configuration from UI

        Returns:
            dict: Encryption results and statistics
        """
        try:
            logger.info(f"Starting custom encryption of file: {input_path}")

            # Load and validate input file
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"Input file not found: {input_path}")

            # Read file content
            with open(input_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            original_size = len(original_content)

            # Create backup (only for non-temporary files)
            backup_path = None
            try:
                # Skip backup for temporary files (they start with temp_)
                if not os.path.basename(input_path).startswith('temp_'):
                    backup_path = self.file_manager.create_backup(input_path)
                    logger.info(f"Backup created: {backup_path}")
                else:
                    logger.info("Skipping backup for temporary file")
            except Exception as backup_error:
                logger.warning(f"Backup creation failed: {backup_error}")
                # Continue without backup for temporary files

            # Determine output path
            if not output_path:
                output_path = self._generate_output_path(input_path, suffix='_encrypted')

            # Set default encryption config if not provided
            if not encryption_config:
                encryption_config = {
                    'level': 'basic',
                    'productionSafe': True,
                    'loadBalancerCompatible': True,
                    'antiDebugging': True,
                    'integrityCheck': False,
                    'domainLocking': False,
                    'allowedDomains': []
                }

            # Apply custom encryption
            encrypted_content = self.custom_encryption.encrypt_content(original_content, encryption_config)
            encrypted_size = len(encrypted_content)

            # Write encrypted file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(encrypted_content)

            # Calculate protection level
            protection_level = self._calculate_protection_level(encryption_config)

            logger.info(f"Custom encryption completed: {output_path}")

            return {
                'status': 'success',
                'input_path': input_path,
                'output_path': output_path,
                'backup_path': backup_path,
                'original_size': original_size,
                'encrypted_size': encrypted_size,
                'protection_level': protection_level,
                'encryption_config': encryption_config,
                'size_increase_percent': ((encrypted_size - original_size) / original_size) * 100 if original_size > 0 else 0
            }

        except Exception as e:
            logger.error(f"Custom encryption failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'input_path': input_path
            }

    def create_enhanced_encrypted_file(self, input_path, output_path=None, encryption_config=None):
        """
        Create an enhanced encrypted file using the robust encryption engine.

        Args:
            input_path (str): Path to the input file
            output_path (str): Path for the encrypted output file
            encryption_config (dict): Encryption configuration from UI

        Returns:
            dict: Encryption results and statistics
        """
        try:
            logger.info(f"Starting enhanced encryption of file: {input_path}")

            # Use the enhanced encryption engine
            result = self.enhanced_encryption.encrypt_file_with_fallbacks(
                input_path, output_path, encryption_config
            )

            if result['status'] == 'success':
                logger.info(f"Enhanced encryption completed: {result['output_path']}")
            else:
                logger.error(f"Enhanced encryption failed: {result['error']}")

            return result

        except Exception as e:
            logger.error(f"Enhanced encryption failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'input_path': input_path
            }

    def _calculate_protection_level(self, config):
        """Calculate protection level based on configuration."""
        level_map = {
            'basic': 40,
            'advanced': 70,
            'maximum': 95
        }

        base_level = level_map.get(config.get('level', 'basic'), 40)

        # Add bonuses for additional features
        if config.get('antiDebugging', False):
            base_level += 5
        if config.get('integrityCheck', False):
            base_level += 5
        if config.get('domainLocking', False):
            base_level += 10
        if not config.get('productionSafe', True):
            base_level += 5  # Higher security but less compatibility

        return min(base_level, 100)

    def start_web_server(self, host='localhost', port=8080):
        """Start the web server for the HTML interface."""
        if not self.web_server:
            raise RuntimeError("Web server not available. Please install Flask.")
        logger.info(f"Starting web server on {host}:{port}")
        self.web_server.run(host=host, port=port)
        
    def _generate_output_path(self, input_path):
        """Generate output path for protected file."""
        path = Path(input_path)
        return str(path.parent / f"{path.stem}_protected{path.suffix}")

def main():
    """Main entry point for command-line interface."""
    parser = argparse.ArgumentParser(
        description='Military-Grade Code Protection Tool',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s protect file.html                    # Protect single file
  %(prog)s protect file.html -o protected.html  # Protect with custom output
  %(prog)s batch /path/to/files                 # Protect directory
  %(prog)s server                               # Start web interface
  %(prog)s server --port 9000                   # Start on custom port
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Protect command
    protect_parser = subparsers.add_parser('protect', help='Protect a single file')
    protect_parser.add_argument('input', help='Input file path')
    protect_parser.add_argument('-o', '--output', help='Output file path')
    protect_parser.add_argument('--strength', type=int, choices=range(1, 11), 
                               default=7, help='Protection strength (1-10)')
    protect_parser.add_argument('--no-backup', action='store_true', 
                               help='Skip creating backup')
    
    # Batch command
    batch_parser = subparsers.add_parser('batch', help='Protect directory')
    batch_parser.add_argument('input_dir', help='Input directory path')
    batch_parser.add_argument('-o', '--output-dir', help='Output directory path')
    batch_parser.add_argument('--patterns', nargs='+', 
                             default=['*.html', '*.css', '*.js', '*.php'],
                             help='File patterns to include')
    
    # Server command
    server_parser = subparsers.add_parser('server', help='Start web interface')
    server_parser.add_argument('--host', default='localhost', help='Server host')
    server_parser.add_argument('--port', type=int, default=8080, help='Server port')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    try:
        protector = MilitaryCodeProtector()
        
        if args.command == 'protect':
            config_overrides = {'strength': args.strength}
            if args.no_backup:
                config_overrides['create_backup'] = False
                
            result = protector.protect_file(args.input, args.output, config_overrides)
            print(f"Protection completed successfully!")
            print(f"Original size: {result.get('original_size', 'N/A')} bytes")
            print(f"Protected size: {result.get('protected_size', 'N/A')} bytes")
            print(f"Protection level: {result.get('protection_level', 'N/A')}%")
            
        elif args.command == 'batch':
            result = protector.protect_directory(args.input_dir, args.output_dir, args.patterns)
            print(f"Batch protection completed!")
            print(f"Total files: {result['total_files']}")
            print(f"Processed: {result['processed']}")
            print(f"Failed: {result['failed']}")
            
        elif args.command == 'server':
            protector.start_web_server(args.host, args.port)
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        logger.error(f"Operation failed: {str(e)}")
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
