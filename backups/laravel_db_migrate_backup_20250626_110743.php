<?php
/**
 * Laravel Database Migration Tool for cPanel Shared Hosting
 * 
 * This script allows you to run database migrations on shared hosting
 * where terminal access is limited.
 * 
 * ⚠️ SECURITY WARNING: This script is potentially dangerous as it allows
 * direct database modifications. Use it only temporarily and delete immediately after use.
 * 
 * Usage: Upload this script to your public/scripts directory and access it via browser.
 */

// Set execution time limit to 300 seconds (5 minutes)
set_time_limit(300);

// Start output buffering
ob_start();

// Define the base directory (<PERSON><PERSON> root)
$baseDir = dirname(dirname(__DIR__));

// Require the autoloader
require $baseDir . '/vendor/autoload.php';

// Load the environment file
$dotenv = Dotenv\Dotenv::createImmutable($baseDir);
$dotenv->load();

// Bootstrap Laravel
$app = require_once $baseDir . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// HTML header
echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Database Migration Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2d3748;
        }
        .success {
            color: #38a169;
            background-color: #f0fff4;
            border-left: 4px solid #38a169;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .warning {
            color: #d69e2e;
            background-color: #fffaf0;
            border-left: 4px solid #d69e2e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .error {
            color: #e53e3e;
            background-color: #fff5f5;
            border-left: 4px solid #e53e3e;
            padding: 8px 12px;
            margin: 8px 0;
        }
        .info {
            color: #3182ce;
            background-color: #ebf8ff;
            border-left: 4px solid #3182ce;
            padding: 8px 12px;
            margin: 8px 0;
        }
        code {
            background-color: #f7fafc;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
        }
        pre {
            background-color: #f7fafc;
            padding: 12px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .command-output {
            background-color: #1a202c;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 5px;
            font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
        }
        .card {
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            padding: 1rem;
        }
        .card h3 {
            margin-top: 0;
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"], select {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }
        button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3182ce;
        }
        .security-warning {
            background-color: #742a2a;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #e2e8f0;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f7fafc;
        }
        tr:nth-child(even) {
            background-color: #f7fafc;
        }
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 12px;
            font-weight: 600;
        }
        .badge-success {
            background-color: #c6f6d5;
            color: #22543d;
        }
        .badge-pending {
            background-color: #fefcbf;
            color: #744210;
        }
        .badge-danger {
            background-color: #fed7d7;
            color: #822727;
        }
    </style>
</head>
<body>
    <div class="security-warning">
        ⚠️ SECURITY WARNING: This script can modify your database schema. Delete this file immediately after use!
    </div>
    <h1>Laravel Database Migration Tool</h1>
';

// Verify database connection
try {
    \Illuminate\Support\Facades\DB::connection()->getPdo();
    echo '<div class="success">Database connection successful: ' . 
        htmlspecialchars(\Illuminate\Support\Facades\DB::connection()->getDatabaseName()) . '</div>';
} catch (\Exception $e) {
    echo '<div class="error">
        <h2>Database Connection Error</h2>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
        <p>Please check your database credentials in the .env file.</p>
    </div>';
    exit;
}

// Check if we should run migrations
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    // Handle migration actions
    if ($action === 'migrate') {
        echo '<h2>Running Migrations</h2>';
        echo '<div class="command-output">';
        
        try {
            // Capture output
            ob_start();
            
            // Run the migration
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $step = isset($_POST['step']) && $_POST['step'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate', [
                '--force' => $force,
                '--step' => $step,
                '--seed' => $seed,
            ]);
            
            $output = ob_get_clean();
            $artisanOutput = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($artisanOutput);
                echo "\n\n" . '<span style="color: #68d391;">Migrations completed successfully!</span>';
            } else {
                echo htmlspecialchars($artisanOutput);
                echo "\n\n" . '<span style="color: #f56565;">Migrations failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</div>';
    } else if ($action === 'migrate:status') {
        echo '<h2>Migration Status</h2>';
        echo '<div class="command-output">';
        
        try {
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:status');
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            echo htmlspecialchars($output);
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</div>';
    } else if ($action === 'migrate:rollback') {
        echo '<h2>Rolling Back Migrations</h2>';
        echo '<div class="command-output">';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $step = isset($_POST['steps']) ? (int)$_POST['steps'] : 1;
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:rollback', [
                '--force' => $force,
                '--step' => $step,
            ]);
            
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #68d391;">Rollback completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #f56565;">Rollback failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</div>';
    } else if ($action === 'migrate:fresh') {
        echo '<h2>Fresh Database Migration</h2>';
        echo '<div class="command-output">';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:fresh', [
                '--force' => $force,
                '--seed' => $seed,
            ]);
            
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #68d391;">Fresh migration completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #f56565;">Fresh migration failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</div>';
    } else if ($action === 'migrate:refresh') {
        echo '<h2>Refreshing Migrations</h2>';
        echo '<div class="command-output">';
        
        try {
            $force = isset($_POST['force']) && $_POST['force'] === 'yes';
            $seed = isset($_POST['seed']) && $_POST['seed'] === 'yes';
            $step = isset($_POST['steps']) ? (int)$_POST['steps'] : 0;
            
            $params = [
                '--force' => $force,
                '--seed' => $seed,
            ];
            
            if ($step > 0) {
                $params['--step'] = $step;
            }
            
            $exitCode = \Illuminate\Support\Facades\Artisan::call('migrate:refresh', $params);
            
            $output = \Illuminate\Support\Facades\Artisan::output();
            
            if ($exitCode === 0) {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #68d391;">Refresh completed successfully!</span>';
            } else {
                echo htmlspecialchars($output);
                echo "\n\n" . '<span style="color: #f56565;">Refresh failed with exit code: ' . $exitCode . '</span>';
            }
        } catch (\Exception $e) {
            echo '<span style="color: #f56565;">Error: ' . htmlspecialchars($e->getMessage()) . '</span>';
        }
        
        echo '</div>';
    }
}

// Show migration status
echo '<h2>Current Migration Status</h2>';
echo '<div class="info">This shows the current status of your migrations.</div>';

try {
    ob_start();
    \Illuminate\Support\Facades\Artisan::call('migrate:status');
    $statusOutput = \Illuminate\Support\Facades\Artisan::output();
    ob_end_clean();
    
    // Parse the status output to create a nice table
    $lines = explode("\n", $statusOutput);
    $migrations = [];
    $headers = [];
    
    foreach ($lines as $i => $line) {
        if (empty(trim($line))) continue;
        
        // Parse header line
        if ($i === 0) {
            $headers = preg_split('/\s{2,}/', trim($line));
            continue;
        }
        
        // Parse data lines
        $data = preg_split('/\s{2,}/', trim($line));
        if (count($data) >= 3) {
            $migrations[] = [
                'status' => trim($data[0]),
                'migration' => trim($data[1]),
                'batch' => isset($data[2]) ? trim($data[2]) : '',
            ];
        }
    }
    
    // Display as a table
    if (!empty($migrations)) {
        echo '<table>
            <thead>
                <tr>
                    <th>Status</th>
                    <th>Migration</th>
                    <th>Batch</th>
                </tr>
            </thead>
            <tbody>';
        
        foreach ($migrations as $migration) {
            $statusClass = $migration['status'] === 'Ran' ? 'badge-success' : 'badge-pending';
            echo '<tr>
                <td><span class="badge ' . $statusClass . '">' . htmlspecialchars($migration['status']) . '</span></td>
                <td>' . htmlspecialchars($migration['migration']) . '</td>
                <td>' . htmlspecialchars($migration['batch']) . '</td>
            </tr>';
        }
        
        echo '</tbody></table>';
    } else {
        echo '<div class="warning">No migration data available.</div>';
    }
} catch (\Exception $e) {
    echo '<div class="error">Error retrieving migration status: ' . htmlspecialchars($e->getMessage()) . '</div>';
}

// Migration actions
echo '
<h2>Migration Actions</h2>
<div class="grid">
    <div class="card">
        <h3>Run Migrations</h3>
        <p>Run pending migrations to update your database schema.</p>
        <form method="post">
            <input type="hidden" name="action" value="migrate">
            <div>
                <label>
                    <input type="checkbox" name="force" value="yes" checked> 
                    Force (run in production)
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" name="step" value="yes"> 
                    Step (run migrations incrementally)
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" name="seed" value="yes"> 
                    Seed (run seeders after migration)
                </label>
            </div>
            <button type="submit">Run Migrations</button>
        </form>
    </div>
    
    <div class="card">
        <h3>Rollback Migrations</h3>
        <p>Rollback the last batch of migrations.</p>
        <form method="post">
            <input type="hidden" name="action" value="migrate:rollback">
            <div>
                <label>
                    <input type="checkbox" name="force" value="yes" checked> 
                    Force (run in production)
                </label>
            </div>
            <div>
                <label for="steps">Steps (number of batches to rollback):</label>
                <input type="number" id="steps" name="steps" value="1" min="1">
            </div>
            <button type="submit">Rollback Migrations</button>
        </form>
    </div>
    
    <div class="card">
        <h3>Refresh Migrations</h3>
        <p>Rollback all migrations and run them again.</p>
        <form method="post">
            <input type="hidden" name="action" value="migrate:refresh">
            <div>
                <label>
                    <input type="checkbox" name="force" value="yes" checked> 
                    Force (run in production)
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" name="seed" value="yes"> 
                    Seed (run seeders after migration)
                </label>
            </div>
            <div>
                <label for="refresh-steps">Steps (limit the number of migrations to be rolled back):</label>
                <input type="number" id="refresh-steps" name="steps" value="0" min="0">
                <small>(0 means all migrations)</small>
            </div>
            <button type="submit">Refresh Migrations</button>
        </form>
    </div>
    
    <div class="card">
        <h3>Fresh Migrations</h3>
        <p><strong>DANGER:</strong> Drop all tables and run migrations from scratch.</p>
        <div class="warning">This will delete all data in your database!</div>
        <form method="post" onsubmit="return confirm(\'WARNING: This will DELETE ALL DATA in your database. Are you sure?\');">
            <input type="hidden" name="action" value="migrate:fresh">
            <div>
                <label>
                    <input type="checkbox" name="force" value="yes" checked> 
                    Force (run in production)
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" name="seed" value="yes"> 
                    Seed (run seeders after migration)
                </label>
            </div>
            <button type="submit" style="background-color: #e53e3e;">Fresh Migrations</button>
        </form>
    </div>
</div>

<div class="info" style="margin-top: 20px;">
    <h3>Security Notice</h3>
    <p>⚠️ This script provides direct access to modify your database structure. For security:</p>
    <ul>
        <li>Delete this file immediately after use</li>
        <li>Consider password-protecting this directory</li>
        <li>Always backup your database before running migrations</li>
    </ul>
</div>

</body>
</html>
'; 