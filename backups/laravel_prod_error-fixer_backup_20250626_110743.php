<?php

// IMPORTANT: DELETE THIS FILE IMMEDIATELY AFTER USE!
// Access this script via your browser: https://yourdomain.com/scripts/laravel_prod_error-fixer.php

// Define the base path of the <PERSON><PERSON> application
// This script is in public/scripts, so <PERSON><PERSON> root is ../../
$basePath = __DIR__ . '/../../';

// Initialize results tracking
$results = [
    'hot_file' => ['status' => 'pending', 'message' => '', 'details' => []],
    'directories' => ['status' => 'pending', 'message' => '', 'details' => []],
    'cache_clear' => ['status' => 'pending', 'message' => '', 'details' => []],
    'env_check' => ['status' => 'pending', 'message' => '', 'details' => []],
    'storage_link' => ['status' => 'pending', 'message' => '', 'details' => []]
];

// Note: Cache clearing now uses <PERSON><PERSON>'s internal Artisan facade for better reliability

// --- Step 1: Check for and remove hot file ---
function checkHotFile($basePath) {
    global $results;
    $hotFilePath = $basePath . 'public/hot';

    if (file_exists($hotFilePath)) {
        if (unlink($hotFilePath)) {
            $results['hot_file']['status'] = 'success';
            $results['hot_file']['message'] = 'Hot file found and successfully deleted';
            $results['hot_file']['details'][] = "Deleted: $hotFilePath";
        } else {
            $results['hot_file']['status'] = 'error';
            $results['hot_file']['message'] = 'Hot file found but could not be deleted';
            $results['hot_file']['details'][] = "Failed to delete: $hotFilePath";
        }
    } else {
        $results['hot_file']['status'] = 'success';
        $results['hot_file']['message'] = 'No hot file found (good for production)';
        $results['hot_file']['details'][] = "Checked: $hotFilePath - not found";
    }
}

// --- Step 2: Check and create directories ---
function ensureDirectories($basePath) {
    global $results;
    $pathsToEnsure = [
        'storage/framework/views',
        'storage/framework/cache/data', // For file cache driver
        'storage/framework/sessions',
        'storage/logs',
        'storage/app/public', // Often used with storage:link
        'bootstrap/cache',    // For compiled services, packages, config cache
    ];

    $errors = [];
    $successes = [];

    foreach ($pathsToEnsure as $relativePath) {
        $path = $basePath . $relativePath;
        $parentDir = dirname($path);

        // Ensure parent directory exists first
        if (!is_dir($parentDir)) {
            if (mkdir($parentDir, 0775, true)) {
                $successes[] = "Parent directory created: $parentDir";
            } else {
                if (!is_dir($parentDir)){
                    $errors[] = "Failed to create parent directory: $parentDir";
                    continue;
                }
            }
        }

        // Now ensure the target directory exists
        if (!is_dir($path)) {
            if (mkdir($path, 0775, true)) {
                $successes[] = "Directory created: $path";
            } else {
                if (!is_dir($path)) {
                    $errors[] = "Failed to create directory: $path";
                    continue;
                }
            }
        } else {
            $successes[] = "Directory exists: $path";
        }

        // Attempt to make the directory writable if it exists
        if (is_dir($path)) {
            if (!is_writable($path)) {
                if (chmod($path, 0775)) {
                    $successes[] = "Permissions set to 0775 for: $path";
                } else {
                    $errors[] = "Failed to set 0775 permissions for: $path";
                }
            } else {
                $successes[] = "Directory is writable: $path";
            }
        } else {
            $errors[] = "Directory $path reported as not existing after creation attempt";
        }
    }

    $results['directories']['details'] = array_merge($successes, $errors);
    if (empty($errors)) {
        $results['directories']['status'] = 'success';
        $results['directories']['message'] = 'All directories created and configured successfully';
    } else {
        $results['directories']['status'] = 'error';
        $results['directories']['message'] = count($errors) . ' directory issues found';
    }
}

// --- Step 3: Clear Caches using Laravel's Internal Artisan ---
function clearCaches($basePath) {
    global $results;
    $commands = [
        'cache:clear' => 'Clear application cache',
        'config:clear' => 'Clear configuration cache',
        'route:clear' => 'Clear route cache',
        'view:clear' => 'Clear view cache',
        'optimize:clear' => 'Clear optimized files'
    ];

    $commandResults = [];
    $errors = [];

    try {
        // Require the autoloader
        require_once $basePath . '/vendor/autoload.php';

        // Load the environment file
        $dotenv = Dotenv\Dotenv::createImmutable($basePath);
        $dotenv->load();

        // Bootstrap Laravel
        $app = require_once $basePath . '/bootstrap/app.php';
        $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
        $kernel->bootstrap();

        foreach ($commands as $command => $description) {
            try {
                $exitCode = \Illuminate\Support\Facades\Artisan::call($command);
                $output = \Illuminate\Support\Facades\Artisan::output();

                $success = ($exitCode === 0);
                $commandResults[] = [
                    'command' => $command,
                    'description' => $description,
                    'success' => $success,
                    'message' => $success ? 'Command executed successfully' : "Failed with exit code: $exitCode",
                    'output' => trim($output)
                ];

                if (!$success) {
                    $errors[] = "$description failed (exit code: $exitCode)";
                }
            } catch (\Exception $e) {
                $commandResults[] = [
                    'command' => $command,
                    'description' => $description,
                    'success' => false,
                    'message' => 'Exception: ' . $e->getMessage(),
                    'output' => ''
                ];
                $errors[] = "$description failed: " . $e->getMessage();
            }
        }
    } catch (\Exception $e) {
        $commandResults[] = [
            'command' => 'Laravel Bootstrap',
            'description' => 'Initialize Laravel framework',
            'success' => false,
            'message' => 'Failed to bootstrap Laravel: ' . $e->getMessage(),
            'output' => ''
        ];
        $errors[] = "Laravel bootstrap failed: " . $e->getMessage();
    }

    $results['cache_clear']['details'] = $commandResults;
    if (empty($errors)) {
        $results['cache_clear']['status'] = 'success';
        $results['cache_clear']['message'] = 'All caches cleared successfully';
    } else {
        $results['cache_clear']['status'] = 'error';
        $results['cache_clear']['message'] = count($errors) . ' cache clearing issues found';
    }
}

// --- Step 4: Check .env file ---
function checkEnvFile($basePath) {
    global $results;
    $envPath = $basePath . '.env';

    if (file_exists($envPath)) {
        if (is_readable($envPath)) {
            $results['env_check']['status'] = 'success';
            $results['env_check']['message'] = '.env file exists and is readable';
            $results['env_check']['details'][] = "Found at: $envPath";
        } else {
            $results['env_check']['status'] = 'error';
            $results['env_check']['message'] = '.env file exists but is NOT readable';
            $results['env_check']['details'][] = "Critical issue: File not readable at $envPath";
        }
    } else {
        $results['env_check']['status'] = 'error';
        $results['env_check']['message'] = '.env file does NOT exist';
        $results['env_check']['details'][] = "Critical error: File missing at $envPath";
    }
}

// --- Step 5: Check storage link ---
function checkStorageLink($basePath) {
    global $results;
    $publicStoragePath = $basePath . 'public/storage';

    if (is_link($publicStoragePath)) {
        $linkTarget = readlink($publicStoragePath);
        $expectedTargetResolved = realpath($basePath . 'storage/app/public');
        $linkTargetResolved = realpath($publicStoragePath);

        if ($linkTargetResolved && $expectedTargetResolved && $linkTargetResolved === $expectedTargetResolved) {
            $results['storage_link']['status'] = 'success';
            $results['storage_link']['message'] = 'Storage link exists and points correctly';
            $results['storage_link']['details'][] = "Link: $publicStoragePath → $linkTarget";
        } else {
            $results['storage_link']['status'] = 'warning';
            $results['storage_link']['message'] = 'Storage link exists but may not be pointing correctly';
            $results['storage_link']['details'][] = "Link: $publicStoragePath → $linkTarget";
            $results['storage_link']['details'][] = "Expected: " . ($expectedTargetResolved ?: $basePath . 'storage/app/public (could not resolve)');
        }
    } else {
        $results['storage_link']['status'] = 'warning';
        $results['storage_link']['message'] = 'Storage link does not exist';
        $results['storage_link']['details'][] = "Missing: $publicStoragePath";
        $results['storage_link']['details'][] = "Run 'php artisan storage:link' if you use the 'public' disk";
    }
}

// Execute all checks
checkHotFile($basePath);
ensureDirectories($basePath);
clearCaches($basePath);
checkEnvFile($basePath);
checkStorageLink($basePath);

// Calculate overall status
$overallStatus = 'success';
$errorCount = 0;
$warningCount = 0;

foreach ($results as $check) {
    if ($check['status'] === 'error') {
        $overallStatus = 'error';
        $errorCount++;
    } elseif ($check['status'] === 'warning') {
        if ($overallStatus !== 'error') {
            $overallStatus = 'warning';
        }
        $warningCount++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laravel Production Error Fixer</title>
    <style>
        :root {
            --radius: 0.65rem;
            --background: oklch(1 0 0);
            --foreground: oklch(0.141 0.005 285.823);
            --card: oklch(1 0 0);
            --card-foreground: oklch(0.141 0.005 285.823);
            --primary: oklch(0.623 0.214 259.815);
            --primary-foreground: oklch(0.97 0.014 254.604);
            --secondary: oklch(0.967 0.001 286.375);
            --secondary-foreground: oklch(0.21 0.006 285.885);
            --muted: oklch(0.967 0.001 286.375);
            --muted-foreground: oklch(0.552 0.016 285.938);
            --border: oklch(0.92 0.004 286.32);
            --success: oklch(0.646 0.222 41.116);
            --success-foreground: oklch(0.97 0.014 254.604);
            --warning: oklch(0.828 0.189 84.429);
            --warning-foreground: oklch(0.141 0.005 285.823);
            --destructive: oklch(0.577 0.245 27.325);
            --destructive-foreground: oklch(0.97 0.014 254.604);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif;
            background-color: var(--background);
            color: var(--foreground);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .header p {
            color: var(--muted-foreground);
            font-size: 1.1rem;
        }

        .status-overview {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: calc(var(--radius) - 2px);
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }

        .status-success {
            background: var(--success);
            color: var(--success-foreground);
        }

        .status-warning {
            background: var(--warning);
            color: var(--warning-foreground);
        }

        .status-error {
            background: var(--destructive);
            color: var(--destructive-foreground);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            padding: 1.5rem;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
            transition: all 0.2s ease;
        }

        .card:hover {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.875rem;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--card-foreground);
        }

        .card-message {
            color: var(--muted-foreground);
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .details {
            background: var(--muted);
            border-radius: calc(var(--radius) - 4px);
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.75rem;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }

        .details-item {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0;
            border-bottom: 1px solid var(--border);
        }

        .details-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .warning-box {
            background: var(--warning);
            color: var(--warning-foreground);
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 2rem;
            text-align: center;
            font-weight: 600;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border);
            color: var(--muted-foreground);
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem 0.5rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .status-overview {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Laravel Production Error Fixer</h1>
            <p>Automated diagnostics and fixes for common Laravel production issues</p>
        </div>

        <div class="status-overview">
            <?php
            $statusClass = 'status-' . $overallStatus;
            $statusIcon = $overallStatus === 'success' ? '✓' : ($overallStatus === 'warning' ? '⚠' : '✗');
            $statusText = $overallStatus === 'success' ? 'All Checks Passed' :
                         ($overallStatus === 'warning' ? 'Completed with Warnings' : 'Issues Found');
            ?>
            <div class="status-badge <?php echo $statusClass; ?>">
                <?php echo $statusIcon; ?> <?php echo $statusText; ?>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h2 style="margin: 0; font-size: 1.5rem;">Execution Summary</h2>
                <button onclick="window.location.reload()" style="
                    background: var(--primary);
                    color: var(--primary-foreground);
                    border: none;
                    padding: 0.5rem 1rem;
                    border-radius: calc(var(--radius) - 2px);
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.2s ease;
                " onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
                    🔄 Re-execute Script
                </button>
            </div>
            <p>
                <?php if ($overallStatus === 'success'): ?>
                    All checks completed successfully. Your Laravel application should be working properly.
                <?php elseif ($overallStatus === 'warning'): ?>
                    Checks completed with <?php echo $warningCount; ?> warning(s). Review the details below.
                <?php else: ?>
                    Found <?php echo $errorCount; ?> error(s) that need attention. Please review and fix the issues below.
                    <?php
                    // Show specific errors
                    $checkTitles = [
                        'hot_file' => 'Hot File Check',
                        'directories' => 'Directory Setup',
                        'cache_clear' => 'Cache Clearing',
                        'env_check' => 'Environment File',
                        'storage_link' => 'Storage Link'
                    ];
                    $errorList = [];
                    foreach ($results as $key => $result) {
                        if ($result['status'] === 'error') {
                            $errorList[] = $checkTitles[$key] . ': ' . $result['message'];
                        }
                    }
                    if (!empty($errorList)): ?>
                        <br><br><div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <span style="
                                background: var(--destructive);
                                color: var(--destructive-foreground);
                                border-radius: 50%;
                                width: 1.5rem;
                                height: 1.5rem;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                font-size: 0.75rem;
                                font-weight: bold;
                            ">!</span>
                            <strong>Specific Errors:</strong>
                        </div>
                        <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                            <?php foreach ($errorList as $error): ?>
                                <li style="color: var(--destructive); margin: 0.25rem 0;"><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                <?php endif; ?>
            </p>
        </div>

        <div class="grid">
            <?php
            $checkTitles = [
                'hot_file' => 'Hot File Check',
                'directories' => 'Directory Setup',
                'cache_clear' => 'Cache Clearing',
                'env_check' => 'Environment File',
                'storage_link' => 'Storage Link'
            ];

            $checkIcons = [
                'hot_file' => '🔥',
                'directories' => '📁',
                'cache_clear' => '🧹',
                'env_check' => '⚙️',
                'storage_link' => '🔗'
            ];

            foreach ($results as $key => $result):
                $iconClass = 'status-' . $result['status'];
                $icon = $checkIcons[$key];
            ?>
            <div class="card">
                <div class="card-header">
                    <div class="card-icon <?php echo $iconClass; ?>">
                        <?php echo $icon; ?>
                    </div>
                    <h3 class="card-title"><?php echo $checkTitles[$key]; ?></h3>
                </div>

                <div class="card-message">
                    <?php echo htmlspecialchars($result['message']); ?>
                </div>

                <?php if (!empty($result['details'])): ?>
                <div class="details">
                    <?php foreach ($result['details'] as $detail): ?>
                        <div class="details-item">
                            <?php
                            if (is_array($detail)) {
                                echo "<strong>" . htmlspecialchars($detail['description']) . ":</strong><br>";
                                echo "Command: " . htmlspecialchars($detail['command']) . "<br>";
                                echo "Status: " . ($detail['success'] ? '✓ Success' : '✗ Failed') . "<br>";
                                echo "Message: " . htmlspecialchars($detail['message']);
                                if (!empty($detail['output'])) {
                                    echo "<br>Output: " . htmlspecialchars($detail['output']);
                                }
                            } else {
                                echo htmlspecialchars($detail);
                            }
                            ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Execution Log Section -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <div class="card-icon" style="background: var(--muted); color: var(--muted-foreground);">
                    📋
                </div>
                <h3 class="card-title">Execution Log</h3>
            </div>

            <div class="card-message">
                Complete execution log with timestamps and detailed operations
            </div>

            <div class="details" style="max-height: 300px;">
                <?php
                $executionLog = [];
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Script execution started";
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Base path: " . $basePath;

                // Hot file log
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === HOT FILE CHECK ===";
                $hotFilePath = $basePath . 'public/hot';
                if (file_exists($hotFilePath)) {
                    $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Hot file found at: " . $hotFilePath;
                    $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Attempting to delete hot file...";
                    if (unlink($hotFilePath)) {
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "] ✓ Hot file successfully deleted";
                    } else {
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "] ✗ Failed to delete hot file";
                    }
                } else {
                    $executionLog[] = "[" . date('Y-m-d H:i:s') . "] ✓ No hot file found (good for production)";
                }

                // Directory log
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === DIRECTORY SETUP ===";
                foreach ($results['directories']['details'] as $detail) {
                    if (is_string($detail)) {
                        $status = (strpos($detail, 'Failed') !== false || strpos($detail, 'ERROR') !== false) ? '✗' : '✓';
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "] $status " . $detail;
                    }
                }

                // Cache clearing log
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === CACHE CLEARING ===";
                foreach ($results['cache_clear']['details'] as $detail) {
                    if (is_array($detail)) {
                        $status = $detail['success'] ? '✓' : '✗';
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "] $status " . $detail['description'];
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "]   Command: " . $detail['command'];
                        $executionLog[] = "[" . date('Y-m-d H:i:s') . "]   Result: " . $detail['message'];
                        if (!empty($detail['output'])) {
                            $executionLog[] = "[" . date('Y-m-d H:i:s') . "]   Output: " . trim($detail['output']);
                        }
                    }
                }

                // Environment file log
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === ENVIRONMENT FILE CHECK ===";
                foreach ($results['env_check']['details'] as $detail) {
                    $status = (strpos($detail, 'Critical') !== false || strpos($detail, 'missing') !== false) ? '✗' : '✓';
                    $executionLog[] = "[" . date('Y-m-d H:i:s') . "] $status " . $detail;
                }

                // Storage link log
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === STORAGE LINK CHECK ===";
                foreach ($results['storage_link']['details'] as $detail) {
                    $status = (strpos($detail, 'Missing') !== false) ? '⚠' : '✓';
                    $executionLog[] = "[" . date('Y-m-d H:i:s') . "] $status " . $detail;
                }

                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] === EXECUTION COMPLETED ===";
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Overall Status: " . strtoupper($overallStatus);
                $executionLog[] = "[" . date('Y-m-d H:i:s') . "] Errors: $errorCount, Warnings: $warningCount";

                foreach ($executionLog as $logEntry):
                ?>
                    <div class="details-item" style="font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.75rem;">
                        <?php echo htmlspecialchars($logEntry); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="warning-box">
            <strong>⚠️ IMPORTANT SECURITY NOTICE</strong><br>
            DELETE THIS SCRIPT IMMEDIATELY AFTER USE!<br>
            File location: <?php echo htmlspecialchars(__FILE__); ?>
        </div>

        <!-- Creator Details Section -->
        <div class="card" style="margin-top: 3rem; margin-bottom: 2rem; background: var(--primary); color: var(--primary-foreground);">
            <div style="text-align: center;">
                <h3 style="margin-bottom: 1rem; font-size: 1.25rem; color: var(--primary-foreground);">
                    👨‍💻 Created By
                </h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; text-align: left;">
                    <div>
                        <strong style="color: var(--primary-foreground);">Firoz Anam</strong><br>
                        <small style="color: var(--primary-foreground); opacity: 0.9;">Full Stack Developer</small>
                    </div>
                    <div>
                        <strong style="color: var(--primary-foreground);">📧 Email:</strong><br>
                        <a href="mailto:<EMAIL>" style="color: var(--primary-foreground); opacity: 0.9; text-decoration: none;">
                            <EMAIL>
                        </a>
                    </div>
                    <div>
                        <strong style="color: var(--primary-foreground);">🐙 GitHub:</strong><br>
                        <a href="https://github.com/firozanam" target="_blank" style="color: var(--primary-foreground); opacity: 0.9; text-decoration: none;">
                            github.com/firozanam
                        </a>
                    </div>
                    <div>
                        <strong style="color: var(--primary-foreground);">💼 LinkedIn:</strong><br>
                        <a href="https://www.linkedin.com/in/firozanam" target="_blank" style="color: var(--primary-foreground); opacity: 0.9; text-decoration: none;">
                            linkedin.com/in/firozanam
                        </a>
                    </div>
                    <div>
                        <strong style="color: var(--primary-foreground);">📱 WhatsApp:</strong><br>
                        <a href="https://wa.me/8801788544788" target="_blank" style="color: var(--primary-foreground); opacity: 0.9; text-decoration: none;">
                            +8801788544788
                        </a>
                    </div>
                    <div>
                        <strong style="color: var(--primary-foreground);">🌐 Website:</strong><br>
                        <a href="https://neurotechsystem.com" target="_blank" style="color: var(--primary-foreground); opacity: 0.9; text-decoration: none;">
                            neurotechsystem.com
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>Next Steps:</strong><br>
                1. Access your site to verify the fixes worked<br>
                2. Delete this script file from your server<br>
                3. Monitor your application logs for any remaining issues
            </p>
            <p style="margin-top: 1rem;">
                Script executed at: <?php echo date('Y-m-d H:i:s T'); ?>
            </p>
        </div>
    </div>
</body>
</html>
