"""
Encryption Engine for Military-Grade Code Protection
Handles multi-layer encryption and encoding operations.
"""

import base64
import hashlib
import secrets
import string
import random
import json
from typing import Dict, List, Tuple, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .config import ProtectionConfig

class EncryptionEngine:
    """Multi-layer encryption engine for code protection."""
    
    def __init__(self, config: ProtectionConfig):
        """
        Initialize the encryption engine.
        
        Args:
            config (ProtectionConfig): Configuration settings
        """
        self.config = config
        self.encryption_keys = {}
        self.encoding_maps = {}
        
    def apply_encryption(self, content: str, file_type: str) -> str:
        """
        Apply multi-layer encryption to content.

        Args:
            content (str): Content to encrypt
            file_type (str): File type extension

        Returns:
            str: Encrypted content
        """
        encrypted_content = content
        encryption_layers = []

        # Apply multiple encryption layers based on configuration
        for layer in range(self.config.encryption.encryption_layers):

            # Layer 1: Basic encoding
            if self.config.encryption.base64_encoding:
                encrypted_content = self._apply_base64_encoding(encrypted_content)
                encryption_layers.append('base64')

            if self.config.encryption.hex_encoding:
                encrypted_content = self._apply_hex_encoding(encrypted_content)
                encryption_layers.append('hex')

            # Layer 2: Custom encoding with dynamic keys
            if self.config.encryption.custom_key_generation:
                encrypted_content = self._apply_custom_encoding(encrypted_content, layer)
                encryption_layers.append('custom')

            # Layer 3: Advanced substitution cipher
            encrypted_content = self._apply_advanced_substitution(encrypted_content, layer)
            encryption_layers.append('substitution')

            # Layer 4: XOR encryption with dynamic key
            encrypted_content = self._apply_xor_encryption(encrypted_content, layer)
            encryption_layers.append('xor')

        # Layer 5: AES encryption (if enabled) - applied last for maximum security
        if self.config.encryption.aes_encryption:
            encrypted_content = self._apply_aes_encryption(encrypted_content)
            encryption_layers.append('aes')

        # Store encryption sequence for decoder
        self.encryption_keys['layers'] = encryption_layers

        # Wrap encrypted content with decoder
        encrypted_content = self._wrap_with_decoder(encrypted_content, file_type)

        return encrypted_content
        
    def _apply_base64_encoding(self, content: str) -> str:
        """Apply Base64 encoding."""
        encoded = base64.b64encode(content.encode('utf-8')).decode('ascii')
        return encoded
        
    def _apply_hex_encoding(self, content: str) -> str:
        """Apply hexadecimal encoding."""
        hex_encoded = content.encode('utf-8').hex()
        return hex_encoded
        
    def _apply_custom_encoding(self, content: str, layer: int = 0) -> str:
        """Apply custom substitution cipher with layer-specific keys."""
        # Generate a layer-specific random substitution map
        chars = string.ascii_letters + string.digits + string.punctuation + ' \t\n'
        shuffled_chars = list(chars)

        # Use layer number as seed for reproducible but different keys per layer
        random.seed(hash(f"custom_layer_{layer}_{self._generate_dynamic_key()}") % (2**32))
        random.shuffle(shuffled_chars)

        # Create encoding map
        encoding_map = dict(zip(chars, shuffled_chars))
        self.encoding_maps[f'custom_layer_{layer}'] = encoding_map

        # Apply encoding
        encoded = ''.join(encoding_map.get(c, c) for c in content)
        return encoded

    def _apply_advanced_substitution(self, content: str, layer: int = 0) -> str:
        """Apply advanced substitution cipher with mathematical transformations."""
        # Use a mathematical transformation based on character position and layer
        key = hash(f"advanced_layer_{layer}") % 256

        encoded_chars = []
        for i, char in enumerate(content):
            # Apply position-based transformation
            char_code = ord(char)
            transformed = (char_code + key + i) % 256

            # Ensure printable characters
            if transformed < 32:
                transformed += 32
            elif transformed > 126:
                transformed = 32 + (transformed % 95)

            encoded_chars.append(chr(transformed))

        self.encoding_maps[f'advanced_layer_{layer}'] = {'key': key}
        return ''.join(encoded_chars)

    def _apply_xor_encryption(self, content: str, layer: int = 0) -> str:
        """Apply XOR encryption with dynamic key."""
        # Generate a dynamic key based on layer and content
        key_seed = f"xor_layer_{layer}_{len(content)}"
        key = hashlib.md5(key_seed.encode()).hexdigest()

        encrypted_chars = []
        for i, char in enumerate(content):
            key_char = key[i % len(key)]
            encrypted_char = chr(ord(char) ^ ord(key_char))
            encrypted_chars.append(encrypted_char)

        self.encryption_keys[f'xor_layer_{layer}'] = key
        return ''.join(encrypted_chars)
        
    def _apply_aes_encryption(self, content: str) -> str:
        """Apply AES encryption with enhanced security."""
        try:
            # Generate a strong password with multiple entropy sources
            password = self._generate_enhanced_key().encode()
            salt = secrets.token_bytes(32)  # Increased salt size

            # Use higher iteration count for better security
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=200000,  # Doubled iterations
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))

            # Encrypt content
            fernet = Fernet(key)
            encrypted = fernet.encrypt(content.encode('utf-8'))

            # Store key information for decoder with additional obfuscation
            encoded_salt = base64.b64encode(salt).decode()
            encoded_password = base64.b64encode(password).decode()

            self.encryption_keys['aes'] = {
                'salt': self._obfuscate_key_data(encoded_salt),
                'password': self._obfuscate_key_data(encoded_password),
                'iterations': 200000
            }

            return base64.b64encode(encrypted).decode()

        except Exception as e:
            # Fallback to simpler encryption if AES fails
            return self._apply_fallback_encryption(content)
        
    def _generate_dynamic_key(self) -> str:
        """Generate a dynamic encryption key."""
        # Use current timestamp and random data
        import time
        timestamp = str(int(time.time()))
        random_part = secrets.token_hex(16)
        return f"{timestamp}_{random_part}"

    def _generate_enhanced_key(self) -> str:
        """Generate an enhanced encryption key with multiple entropy sources."""
        import time
        import os

        # Combine multiple entropy sources
        timestamp = str(int(time.time() * 1000000))  # Microsecond precision
        random_bytes = secrets.token_hex(32)
        process_id = str(os.getpid())

        # Add system-specific entropy if available
        try:
            system_random = os.urandom(16).hex()
        except:
            system_random = secrets.token_hex(16)

        # Combine all entropy sources
        combined = f"{timestamp}_{random_bytes}_{process_id}_{system_random}"

        # Hash the combined entropy for consistent length
        return hashlib.sha256(combined.encode()).hexdigest()

    def _obfuscate_key_data(self, data: str) -> str:
        """Obfuscate key data to make it less obvious."""
        # Simple obfuscation - reverse and add padding
        reversed_data = data[::-1]
        padding = secrets.token_hex(8)
        return f"{padding}{reversed_data}{padding}"

    def _deobfuscate_key_data(self, obfuscated: str) -> str:
        """Deobfuscate key data."""
        # Remove padding and reverse
        if len(obfuscated) > 16:
            data_part = obfuscated[16:-16]
            return data_part[::-1]
        return obfuscated

    def _apply_fallback_encryption(self, content: str) -> str:
        """Apply fallback encryption if AES fails."""
        # Use multiple layers of simple encryption
        encrypted = content

        # Layer 1: Base64
        encrypted = base64.b64encode(encrypted.encode()).decode()

        # Layer 2: Simple XOR
        key = "fallback_key_" + secrets.token_hex(8)
        xor_encrypted = ""
        for i, char in enumerate(encrypted):
            xor_encrypted += chr(ord(char) ^ ord(key[i % len(key)]))

        # Layer 3: Base64 again
        final_encrypted = base64.b64encode(xor_encrypted.encode('latin1')).decode()

        self.encryption_keys['fallback'] = {
            'key': key,
            'method': 'base64_xor_base64'
        }

        return final_encrypted
        
    def _wrap_with_decoder(self, encrypted_content: str, file_type: str) -> str:
        """Wrap encrypted content with appropriate decoder."""
        if file_type in ['.html', '.htm']:
            return self._wrap_html_decoder(encrypted_content)
        elif file_type in ['.js', '.jsx']:
            return self._wrap_js_decoder(encrypted_content)
        elif file_type == '.php':
            return self._wrap_php_decoder(encrypted_content)
        else:
            return encrypted_content
            
    def _wrap_html_decoder(self, encrypted_content: str) -> str:
        """Wrap with HTML/JavaScript decoder."""
        decoder_script = self._generate_js_decoder()

        # Properly escape the encrypted content for JavaScript in HTML
        escaped_content = encrypted_content.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')

        # Create the wrapped HTML
        wrapped = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Protected Content</title>
</head>
<body>
    <script>
    {decoder_script}

    // Encrypted content
    var encryptedData = "{escaped_content}";

    // Decode and inject
    try {{
        var decoded = decodeContent(encryptedData);
        document.open();
        document.write(decoded);
        document.close();
    }} catch(e) {{
        console.error("Decoding failed:", e);
        document.body.innerHTML = "<h1>Content Protection Active</h1><p>Unable to display protected content.</p>";
    }}
    </script>
</body>
</html>"""
        return wrapped
        
    def _wrap_js_decoder(self, encrypted_content: str) -> str:
        """Wrap with JavaScript decoder."""
        decoder_script = self._generate_js_decoder()

        # Properly escape the encrypted content for JavaScript
        escaped_content = encrypted_content.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')

        wrapped = f"""
{decoder_script}

// Encrypted content
var encryptedData = "{escaped_content}";

// Decode and execute
try {{
    var decoded = decodeContent(encryptedData);
    eval(decoded);
}} catch(e) {{
    console.error("Decoding failed:", e);
}}
"""
        return wrapped
        
    def _wrap_php_decoder(self, encrypted_content: str) -> str:
        """Wrap with PHP decoder."""
        decoder_script = self._generate_php_decoder()

        # Properly escape the encrypted content for PHP
        escaped_content = encrypted_content.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')

        wrapped = f"""<?php
{decoder_script}

// Encrypted content
$encryptedData = "{escaped_content}";

// Decode and execute
try {{
    $decoded = decodeContent($encryptedData);
    eval($decoded);
}} catch(Exception $e) {{
    error_log("Decoding failed: " . $e->getMessage());
}}
?>"""
        return wrapped
        
    def _generate_js_decoder(self) -> str:
        """Generate JavaScript decoder function with support for multiple layers."""
        decoder = """
function decodeContent(encrypted) {
    var content = encrypted;

    // Reverse encryption layers in reverse order
    var layers = """ + json.dumps(self.encryption_keys.get('layers', [])) + """;

    // Reverse AES encryption if applied (always last)
    if (layers.indexOf('aes') !== -1 && typeof aesDecrypt !== 'undefined') {
        content = aesDecrypt(content);
    }

    // Reverse other layers in reverse order
    for (var i = layers.length - 1; i >= 0; i--) {
        var layer = layers[i];

        if (layer === 'xor' && typeof xorDecrypt !== 'undefined') {
            content = xorDecrypt(content, i);
        } else if (layer === 'substitution' && typeof substitutionDecrypt !== 'undefined') {
            content = substitutionDecrypt(content, i);
        } else if (layer === 'custom' && typeof customDecode !== 'undefined') {
            content = customDecode(content, i);
        } else if (layer === 'hex' && typeof hexDecode !== 'undefined') {
            content = hexDecode(content);
        } else if (layer === 'base64' && typeof base64Decode !== 'undefined') {
            content = base64Decode(content);
        }
    }

    return content;
}

function base64Decode(str) {
    try {
        return atob(str);
    } catch(e) {
        return str;
    }
}

function hexDecode(hex) {
    var result = '';
    for (var i = 0; i < hex.length; i += 2) {
        result += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
    }
    return result;
}

function xorDecrypt(content, layer) {
    var key = """ + json.dumps(self.encryption_keys.get('xor_layer_0', '')) + """;
    var result = '';
    for (var i = 0; i < content.length; i++) {
        var keyChar = key.charAt(i % key.length);
        result += String.fromCharCode(content.charCodeAt(i) ^ keyChar.charCodeAt(0));
    }
    return result;
}

function substitutionDecrypt(content, layer) {
    var keyInfo = """ + json.dumps(self.encoding_maps.get('advanced_layer_0', {})) + """;
    var key = keyInfo.key || 0;
    var result = '';

    for (var i = 0; i < content.length; i++) {
        var charCode = content.charCodeAt(i);
        var original = (charCode - key - i + 256) % 256;

        if (original < 32) {
            original += 224;
        } else if (original > 126) {
            original = 32 + ((original - 32) % 95);
        }

        result += String.fromCharCode(original);
    }

    return result;
}
"""
        
        # Add custom decoder if custom encoding was used
        if 'custom' in self.encoding_maps:
            encoding_map = self.encoding_maps['custom']
            # Create reverse map
            reverse_map = {v: k for k, v in encoding_map.items()}
            
            # Convert to JavaScript object
            js_map = json.dumps(reverse_map)
            decoder += f"""
function customDecode(str) {{
    var reverseMap = {js_map};
    var result = '';
    for (var i = 0; i < str.length; i++) {{
        var char = str.charAt(i);
        result += reverseMap[char] || char;
    }}
    return result;
}}
"""
        
        # Add AES decoder if AES encryption was used
        if 'aes' in self.encryption_keys:
            # Note: This is a simplified version. In practice, you'd need a JavaScript AES library
            decoder += """
function aesDecrypt(encrypted) {
    // This would require a JavaScript AES implementation
    // For now, return as-is
    return encrypted;
}
"""
        
        return decoder
        
    def _generate_php_decoder(self) -> str:
        """Generate PHP decoder function with multi-layer support."""
        layers_json = json.dumps(self.encryption_keys.get('layers', []))

        decoder = f"""
function decodeContent($encrypted) {{
    $content = $encrypted;

    // Reverse encryption layers in reverse order
    $layers = json_decode('{layers_json}', true);

    // Reverse AES encryption if applied (always last)
    if (in_array('aes', $layers) && function_exists('aesDecrypt')) {{
        $content = aesDecrypt($content);
    }}

    // Reverse other layers in reverse order
    for ($i = count($layers) - 1; $i >= 0; $i--) {{
        $layer = $layers[$i];

        if ($layer === 'xor' && function_exists('xorDecrypt')) {{
            $content = xorDecrypt($content, $i);
        }} elseif ($layer === 'substitution' && function_exists('substitutionDecrypt')) {{
            $content = substitutionDecrypt($content, $i);
        }} elseif ($layer === 'custom' && function_exists('customDecode')) {{
            $content = customDecode($content, $i);
        }} elseif ($layer === 'hex' && function_exists('hexDecode')) {{
            $content = hexDecode($content);
        }} elseif ($layer === 'base64' && function_exists('base64Decode')) {{
            $content = base64Decode($content);
        }}
    }}

    return $content;
}}

function base64Decode($str) {{
    return base64_decode($str);
}}

function hexDecode($hex) {{
    return hex2bin($hex);
}}

function xorDecrypt($content, $layer) {{
    $key = '{self.encryption_keys.get('xor_layer_0', '')}';
    $result = '';
    for ($i = 0; $i < strlen($content); $i++) {{
        $keyChar = $key[$i % strlen($key)];
        $result .= chr(ord($content[$i]) ^ ord($keyChar));
    }}
    return $result;
}}

function substitutionDecrypt($content, $layer) {{
    $keyInfo = json_decode('{json.dumps(self.encoding_maps.get('advanced_layer_0', {}))}', true);
    $key = isset($keyInfo['key']) ? $keyInfo['key'] : 0;
    $result = '';

    for ($i = 0; $i < strlen($content); $i++) {{
        $charCode = ord($content[$i]);
        $original = ($charCode - $key - $i + 256) % 256;

        if ($original < 32) {{
            $original += 224;
        }} elseif ($original > 126) {{
            $original = 32 + (($original - 32) % 95);
        }}

        $result .= chr($original);
    }}

    return $result;
}}
"""
        
        # Add custom decoder if custom encoding was used
        if 'custom' in self.encoding_maps:
            encoding_map = self.encoding_maps['custom']
            # Create reverse map for PHP
            reverse_map = {v: k for k, v in encoding_map.items()}
            
            decoder += """
function customDecode($str) {
    $reverseMap = """ + str(reverse_map).replace("'", '"') + """;
    $result = '';
    for ($i = 0; $i < strlen($str); $i++) {
        $char = $str[$i];
        $result .= isset($reverseMap[$char]) ? $reverseMap[$char] : $char;
    }
    return $result;
}
"""
        
        return decoder
        
    def generate_encryption_key(self, seed: str = None) -> str:
        """
        Generate a secure encryption key.
        
        Args:
            seed (str): Optional seed for key generation
            
        Returns:
            str: Generated encryption key
        """
        if seed:
            # Use seed for deterministic key generation
            hash_obj = hashlib.sha256(seed.encode())
            return hash_obj.hexdigest()
        else:
            # Generate random key
            return secrets.token_hex(32)
            
    def create_integrity_hash(self, content: str) -> str:
        """
        Create integrity hash for content validation.
        
        Args:
            content (str): Content to hash
            
        Returns:
            str: Integrity hash
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
        
    def obfuscate_strings(self, content: str, strings_to_obfuscate: List[str]) -> Tuple[str, Dict[str, str]]:
        """
        Obfuscate specific strings in content.
        
        Args:
            content (str): Content containing strings
            strings_to_obfuscate (list): List of strings to obfuscate
            
        Returns:
            tuple: (obfuscated_content, string_map)
        """
        string_map = {}
        obfuscated_content = content
        
        for original_string in strings_to_obfuscate:
            # Generate obfuscated version
            if self.config.encryption.base64_encoding:
                obfuscated = base64.b64encode(original_string.encode()).decode()
            else:
                # Simple character substitution
                obfuscated = ''.join(chr(ord(c) + 1) for c in original_string)
                
            string_map[original_string] = obfuscated
            obfuscated_content = obfuscated_content.replace(original_string, obfuscated)
            
        return obfuscated_content, string_map
