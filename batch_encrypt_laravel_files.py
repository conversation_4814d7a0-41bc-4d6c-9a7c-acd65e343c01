#!/usr/bin/env python3
"""
Batch Encryption Utility for Laravel PHP Files
Encrypts all specified Laravel files using the enhanced encryption engine.
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

# Add the military_code_protector to Python path
project_root = Path(__file__).parent
military_protector_path = project_root / 'military_code_protector'
sys.path.insert(0, str(military_protector_path))

from core.enhanced_encryption import EnhancedEncryptionEngine

class LaravelFileBatchEncryptor:
    """Batch encryptor for Laravel PHP files."""
    
    def __init__(self):
        """Initialize the batch encryptor."""
        self.encryption_engine = EnhancedEncryptionEngine()
        self.target_files = [
            'laravel_db_migrate.php',
            'laravel_db_restore.php',
            'laravel_developer_toolkit.php',
            'laravel_npm_build.php',
            'laravel_permissions_fixer.php',
            'laravel_prod_error-fixer.php',
            'laravel_run_artisan.php',
            'laravel_symlink_creator.php'
        ]
        
    def check_files_exist(self) -> dict:
        """Check which target files exist."""
        results = {
            'existing': [],
            'missing': [],
            'total': len(self.target_files)
        }
        
        for file_path in self.target_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                results['existing'].append({
                    'path': file_path,
                    'size': file_size,
                    'size_human': self._format_file_size(file_size)
                })
            else:
                results['missing'].append(file_path)
        
        return results
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def create_encryption_config(self, level: str = 'advanced') -> dict:
        """Create encryption configuration."""
        configs = {
            'basic': {
                'level': 'basic',
                'productionSafe': True,
                'loadBalancerCompatible': True,
                'antiDebugging': True,
                'integrityCheck': False,
                'domainLocking': False,
                'allowedDomains': []
            },
            'standard': {
                'level': 'standard',
                'productionSafe': True,
                'loadBalancerCompatible': True,
                'antiDebugging': True,
                'integrityCheck': True,
                'domainLocking': False,
                'allowedDomains': []
            },
            'advanced': {
                'level': 'advanced',
                'productionSafe': True,
                'loadBalancerCompatible': True,
                'antiDebugging': True,
                'integrityCheck': True,
                'domainLocking': False,
                'allowedDomains': [],
                'multiLayer': True,
                'dynamicKeys': True
            }
        }
        
        return configs.get(level, configs['advanced'])
    
    def encrypt_all_files(self, encryption_level: str = 'advanced', output_dir: str = None) -> dict:
        """Encrypt all target files."""
        print("🔒 Laravel Files Batch Encryption Tool")
        print("=" * 50)
        
        # Check which files exist
        file_check = self.check_files_exist()
        print(f"📁 Found {len(file_check['existing'])} of {file_check['total']} target files")
        
        if file_check['missing']:
            print(f"⚠️  Missing files: {', '.join(file_check['missing'])}")
        
        if not file_check['existing']:
            print("❌ No target files found!")
            return {'status': 'error', 'message': 'No target files found'}
        
        # Display files to be encrypted
        print("\n📋 Files to encrypt:")
        for file_info in file_check['existing']:
            print(f"  • {file_info['path']} ({file_info['size_human']})")
        
        # Create output directory
        if not output_dir:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = f"encrypted_laravel_files_{timestamp}"
        
        os.makedirs(output_dir, exist_ok=True)
        print(f"\n📂 Output directory: {output_dir}")
        
        # Create encryption configuration
        encryption_config = self.create_encryption_config(encryption_level)
        print(f"🔧 Encryption level: {encryption_level}")
        
        # Get list of existing file paths
        existing_files = [file_info['path'] for file_info in file_check['existing']]
        
        # Start batch encryption
        print(f"\n🚀 Starting encryption of {len(existing_files)} files...")
        print("-" * 50)
        
        results = self.encryption_engine.batch_encrypt_files(
            existing_files, 
            output_dir, 
            encryption_config
        )
        
        # Display results
        print(f"\n✅ Batch encryption completed!")
        print(f"📊 Results: {results['successful']} successful, {results['failed']} failed")
        print(f"⏱️  Processing time: {results['processing_time']:.2f} seconds")
        
        # Display detailed results
        print(f"\n📋 Detailed Results:")
        for result in results['results']:
            status_icon = "✅" if result['status'] == 'success' else "❌"
            file_name = os.path.basename(result['file_path'])
            
            if result['status'] == 'success':
                method = result.get('encryption_method', 'unknown')
                size_increase = result.get('stats', {}).get('size_increase_percent', 0)
                print(f"  {status_icon} {file_name} - {method} method (+{size_increase:.1f}%)")
                
                if result.get('warnings'):
                    for warning in result['warnings']:
                        print(f"    ⚠️  {warning}")
            else:
                print(f"  {status_icon} {file_name} - ERROR: {result.get('error', 'Unknown error')}")
        
        # Save detailed report
        report_file = os.path.join(output_dir, 'encryption_report.json')
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📄 Detailed report saved: {report_file}")
        
        return results
    
    def test_encrypted_files(self, output_dir: str) -> dict:
        """Test that encrypted files can be executed."""
        print(f"\n🧪 Testing encrypted files in {output_dir}...")
        
        test_results = {
            'tested': 0,
            'passed': 0,
            'failed': 0,
            'results': []
        }
        
        for file_name in os.listdir(output_dir):
            if file_name.endswith('.php') and file_name.startswith('encrypted_'):
                file_path = os.path.join(output_dir, file_name)
                test_results['tested'] += 1
                
                try:
                    # Basic syntax check
                    result = os.system(f"php -l {file_path} > /dev/null 2>&1")
                    if result == 0:
                        test_results['passed'] += 1
                        test_results['results'].append({
                            'file': file_name,
                            'status': 'passed',
                            'message': 'Syntax check passed'
                        })
                        print(f"  ✅ {file_name} - Syntax OK")
                    else:
                        test_results['failed'] += 1
                        test_results['results'].append({
                            'file': file_name,
                            'status': 'failed',
                            'message': 'Syntax check failed'
                        })
                        print(f"  ❌ {file_name} - Syntax Error")
                        
                except Exception as e:
                    test_results['failed'] += 1
                    test_results['results'].append({
                        'file': file_name,
                        'status': 'error',
                        'message': str(e)
                    })
                    print(f"  ❌ {file_name} - Test Error: {str(e)}")
        
        print(f"\n🧪 Testing completed: {test_results['passed']}/{test_results['tested']} files passed")
        return test_results

def main():
    """Main function."""
    encryptor = LaravelFileBatchEncryptor()
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description='Batch encrypt Laravel PHP files')
    parser.add_argument('--level', choices=['basic', 'standard', 'advanced'], 
                       default='advanced', help='Encryption level')
    parser.add_argument('--output', help='Output directory')
    parser.add_argument('--test', action='store_true', help='Test encrypted files after encryption')
    
    args = parser.parse_args()
    
    try:
        # Encrypt files
        results = encryptor.encrypt_all_files(args.level, args.output)
        
        if results.get('status') == 'error':
            print(f"❌ Error: {results.get('message')}")
            sys.exit(1)
        
        # Test files if requested
        if args.test and results['successful'] > 0:
            output_dir = args.output or f"encrypted_laravel_files_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            encryptor.test_encrypted_files(output_dir)
        
        print(f"\n🎉 All done! {results['successful']} files encrypted successfully.")
        
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
