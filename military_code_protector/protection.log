2025-06-26 07:48:50,086 - __main__ - INFO - Starting web server on 127.0.0.1:8080
2025-06-26 07:48:59,333 - __main__ - INFO - Starting web server on 127.0.0.1:8081
2025-06-26 07:48:59,338 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-06-26 07:48:59,338 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 07:49:15,880 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.php
2025-06-26 07:49:15,881 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:49:15,881 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_test.php
2025-06-26 07:49:15,881 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:49:15] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:51:40,455 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:51:40,455 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:51:40,456 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:51:40,457 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:51:40] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:52:20,706 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:52:20] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:52:27,668 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:52:27] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:52:41,307 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:52:41,307 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:52:41,309 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:52:41,309 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:52:41] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:53:10,909 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:53:10] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:54:13,144 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:54:13,144 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:54:13,152 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:54:13,153 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:54:13] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:54:59,967 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:54:59,967 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:54:59,968 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:54:59,969 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:54:59] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:55:58,239 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:55:58,239 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:55:58,241 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:55:58,241 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:55:58] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:56:57,170 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:56:57,170 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:56:57,171 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:56:57,171 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:56:57] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:57:19,184 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.js
2025-06-26 07:57:19,185 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:57:19,186 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_test.js
2025-06-26 07:57:19,186 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:57:19] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:57:39,288 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:57:39] "GET / HTTP/1.1" 200 -
2025-06-26 07:57:39,366 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:57:39] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-26 07:58:50,807 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:58:50] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 07:58:57,318 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_laravel_symlink_creator.php
2025-06-26 07:58:57,319 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:58:57,321 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_laravel_symlink_creator.php
2025-06-26 07:58:57,321 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:58:57] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:59:03,520 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:03] "GET /api/download/encrypted_laravel_symlink_creator.php HTTP/1.1" 200 -
2025-06-26 07:59:03,555 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:03] "GET /api/download/encrypted_laravel_symlink_creator.php HTTP/1.1" 200 -
2025-06-26 07:59:18,575 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/laravel_symlink_creator.php
2025-06-26 07:59:18,577 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_laravel_symlink_creator.php
2025-06-26 07:59:18,577 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:59:18,578 - __main__ - INFO - Backup created: backups/laravel_symlink_creator_backup_20250626_075918.php
2025-06-26 07:59:18,587 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_laravel_symlink_creator.php
2025-06-26 07:59:18,606 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:18] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:59:18,753 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_laravel_symlink_creator.php
2025-06-26 07:59:18,753 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:18] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:59:36,538 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:36] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 07:59:40,261 - __main__ - INFO - Starting protection of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/laravel_run_artisan.php
2025-06-26 07:59:40,263 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_laravel_symlink_creator.php
2025-06-26 07:59:40,264 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:59:40,265 - __main__ - INFO - Backup created: backups/laravel_run_artisan_backup_20250626_075940.php
2025-06-26 07:59:40,265 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_laravel_symlink_creator.php
2025-06-26 07:59:40,294 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_laravel_run_artisan.php
2025-06-26 07:59:40,313 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 07:59:40,322 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_laravel_run_artisan.php
2025-06-26 07:59:40,322 - __main__ - INFO - Protection completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/protected_laravel_run_artisan.php
2025-06-26 07:59:40,323 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:40] "POST /api/protect HTTP/1.1" 200 -
2025-06-26 07:59:40,323 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:40] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 07:59:57,710 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:57] "GET /api/download/encrypted_laravel_run_artisan.php HTTP/1.1" 200 -
2025-06-26 07:59:57,751 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 07:59:57] "GET /api/download/encrypted_laravel_run_artisan.php HTTP/1.1" 200 -
2025-06-26 08:00:00,950 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:00:00] "GET /api/download/encrypted_laravel_run_artisan.php HTTP/1.1" 200 -
2025-06-26 08:02:31,660 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:02:31] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:02:33,075 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:02:33] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:02:58,055 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:02:58] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:02:59,827 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:02:59] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:09:08,643 - __main__ - INFO - Starting protection of file: ../laravel_symlink_creator.php
2025-06-26 08:09:08,644 - __main__ - INFO - Backup created: backups/laravel_symlink_creator_backup_20250626_080908.php
2025-06-26 08:09:08,801 - __main__ - INFO - Protection completed: ../test_encryption/protected_laravel_symlink.php
2025-06-26 08:11:38,028 - __main__ - INFO - Starting protection of file: ../laravel_symlink_creator.php
2025-06-26 08:11:38,029 - __main__ - INFO - Backup created: backups/laravel_symlink_creator_backup_20250626_081138.php
2025-06-26 08:11:38,192 - __main__ - INFO - Protection completed: ../test_encryption/protected_laravel_symlink_fixed.php
2025-06-26 08:13:58,492 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:13:58] "GET / HTTP/1.1" 200 -
2025-06-26 08:14:11,802 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:11] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 08:14:18,492 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:18] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:14:21,334 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:21] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:14:24,242 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:24] "GET / HTTP/1.1" 200 -
2025-06-26 08:14:37,596 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:37] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 08:14:43,942 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:43] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:14:50,515 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:14:50] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:15:01,891 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:15:01] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:15:15,803 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:15:15] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:15:20,697 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:15:20] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:15:32,086 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:15:32] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:16:36,070 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/temp_input_test.php
2025-06-26 08:16:36,071 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 08:16:36,071 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmprdqgeznp/encrypted_test.php
2025-06-26 08:16:36,071 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:16:36] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:19:32,149 - __main__ - INFO - Starting web server on 127.0.0.1:8081
2025-06-26 08:19:32,153 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:8081
2025-06-26 08:19:32,153 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-26 08:19:49,114 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_test.php
2025-06-26 08:19:49,114 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 08:19:49,114 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_test.php
2025-06-26 08:19:49,115 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:19:49] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 08:20:04,138 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 08:20:04] "GET / HTTP/1.1" 200 -
2025-06-26 09:05:28,382 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:05:28] "GET / HTTP/1.1" 200 -
2025-06-26 09:05:57,988 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:05:57] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 09:06:04,974 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_migrate.php
2025-06-26 09:06:04,975 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 09:06:04,976 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_migrate.php
2025-06-26 09:06:04,977 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:06:04] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 09:06:12,978 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:06:12] "GET /api/download/encrypted_laravel_db_migrate.php HTTP/1.1" 200 -
2025-06-26 09:06:13,006 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:06:13] "GET /api/download/encrypted_laravel_db_migrate.php HTTP/1.1" 200 -
2025-06-26 09:13:29,896 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:13:29] "GET / HTTP/1.1" 200 -
2025-06-26 09:13:29,933 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:13:29] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-26 09:13:43,876 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 09:13:43] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 10:39:35,275 - __main__ - INFO - Starting protection of file: ../laravel_db_migrate_tool.php
2025-06-26 10:39:35,276 - __main__ - INFO - Backup created: backups/laravel_db_migrate_tool_backup_20250626_103935.php
2025-06-26 10:39:35,335 - __main__ - INFO - Protection completed: ../encrypted_laravel_db_migrate_tool.php
2025-06-26 10:39:43,385 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:39:43] "[35m[1mPOST /api/encrypt HTTP/1.1[0m" 500 -
2025-06-26 10:41:42,651 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_migrate_tool.php
2025-06-26 10:41:42,651 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:41:42,652 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_migrate_tool.php
2025-06-26 10:41:42,652 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:41:42] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:41:42,654 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:41:42] "GET /api/download/encrypted_laravel_db_migrate_tool.php HTTP/1.1" 200 -
2025-06-26 10:43:58,538 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:43:58] "GET / HTTP/1.1" 200 -
2025-06-26 10:44:21,175 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:44:21] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 10:44:26,718 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:44:26] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:44:26,720 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_restore.php
2025-06-26 10:44:26,720 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:44:26,720 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_restore.php
2025-06-26 10:44:26,721 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:44:26] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:44:45,198 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:44:45] "GET /api/download/encrypted_laravel_db_restore.php HTTP/1.1" 200 -
2025-06-26 10:44:45,231 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:44:45] "GET /api/download/encrypted_laravel_db_restore.php HTTP/1.1" 200 -
2025-06-26 10:47:00,894 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:47:00] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 10:47:08,020 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:47:08] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:47:08,023 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_restore.php
2025-06-26 10:47:08,023 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:47:08,024 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_restore.php
2025-06-26 10:47:08,025 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_symlink_creator.php
2025-06-26 10:47:08,025 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:47:08,026 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_symlink_creator.php
2025-06-26 10:47:08,027 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:47:08] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:47:11,709 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:47:11] "GET /api/download/encrypted_laravel_symlink_creator.php HTTP/1.1" 200 -
2025-06-26 10:47:11,748 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:47:11] "GET /api/download/encrypted_laravel_symlink_creator.php HTTP/1.1" 200 -
2025-06-26 10:48:56,825 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:48:56] "GET / HTTP/1.1" 200 -
2025-06-26 10:49:48,794 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:49:48] "POST /api/upload HTTP/1.1" 200 -
2025-06-26 10:49:57,323 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:49:57] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:49:57,324 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_migrate_tool.php
2025-06-26 10:49:57,325 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:49:57,325 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_migrate_tool.php
2025-06-26 10:49:57,325 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:49:57] "POST /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:50:01,863 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:50:01] "GET /api/download/encrypted_laravel_db_migrate_tool.php HTTP/1.1" 200 -
2025-06-26 10:50:01,946 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:50:01] "GET /api/download/encrypted_laravel_db_migrate_tool.php HTTP/1.1" 200 -
2025-06-26 10:51:53,506 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:51:53] "OPTIONS /api/encrypt HTTP/1.1" 200 -
2025-06-26 10:51:53,508 - __main__ - INFO - Starting custom encryption of file: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/temp_input_laravel_db_migrate_tool.php
2025-06-26 10:51:53,508 - __main__ - INFO - Skipping backup for temporary file
2025-06-26 10:51:53,509 - __main__ - INFO - Custom encryption completed: /var/folders/3y/h94wscvn5259kbkrg8dz9c1c0000gp/T/tmp3eba_kai/encrypted_laravel_db_migrate_tool.php
2025-06-26 10:51:53,509 - werkzeug - INFO - 127.0.0.1 - - [26/Jun/2025 10:51:53] "POST /api/encrypt HTTP/1.1" 200 -
