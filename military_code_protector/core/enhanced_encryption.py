"""
Enhanced Encryption Engine for Military-Grade Code Protection
Provides robust encryption with multiple fallback mechanisms and comprehensive error handling.
"""

import os
import sys
import base64
import secrets
import hashlib
import logging
import chardet
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import json
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.custom_encryption import CustomEncryptionEngine
from utils.file_manager import FileManager

class EnhancedEncryptionEngine:
    """Enhanced encryption engine with robust error handling and multiple fallback mechanisms."""
    
    def __init__(self):
        """Initialize the enhanced encryption engine."""
        self.logger = self._setup_logging()
        self.file_manager = FileManager()
        self.custom_encryption = CustomEncryptionEngine()
        self.supported_encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'ascii']
        self.chunk_size = 8192  # 8KB chunks for large files
        
    def _setup_logging(self) -> logging.Logger:
        """Setup detailed logging for encryption operations."""
        logger = logging.getLogger('enhanced_encryption')
        logger.setLevel(logging.DEBUG)
        
        # Create file handler
        log_file = project_root / 'enhanced_encryption.log'
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        if not logger.handlers:
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def detect_file_encoding(self, file_path: str) -> str:
        """Detect the encoding of a file using multiple methods."""
        try:
            # Method 1: Use chardet for automatic detection
            with open(file_path, 'rb') as f:
                raw_data = f.read(10240)  # Read first 10KB
                result = chardet.detect(raw_data)
                if result['confidence'] > 0.7:
                    detected_encoding = result['encoding']
                    self.logger.debug(f"Chardet detected encoding: {detected_encoding} (confidence: {result['confidence']})")
                    return detected_encoding
            
            # Method 2: Try common encodings
            for encoding in self.supported_encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1024)  # Try to read first 1KB
                    self.logger.debug(f"Successfully detected encoding: {encoding}")
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            # Fallback to utf-8 with error handling
            self.logger.warning(f"Could not detect encoding for {file_path}, using utf-8 with error handling")
            return 'utf-8'
            
        except Exception as e:
            self.logger.error(f"Error detecting encoding for {file_path}: {str(e)}")
            return 'utf-8'
    
    def read_file_content(self, file_path: str) -> Tuple[str, str]:
        """Read file content with robust encoding handling."""
        detected_encoding = self.detect_file_encoding(file_path)
        
        # Try the detected encoding first
        for encoding in [detected_encoding] + [enc for enc in self.supported_encodings if enc != detected_encoding]:
            try:
                with open(file_path, 'r', encoding=encoding, errors='replace') as f:
                    content = f.read()
                self.logger.info(f"Successfully read {file_path} with encoding: {encoding}")
                return content, encoding
            except Exception as e:
                self.logger.debug(f"Failed to read {file_path} with encoding {encoding}: {str(e)}")
                continue
        
        # Final fallback: read as binary and decode with error handling
        try:
            with open(file_path, 'rb') as f:
                raw_content = f.read()
            content = raw_content.decode('utf-8', errors='replace')
            self.logger.warning(f"Used binary fallback for {file_path}")
            return content, 'binary-fallback'
        except Exception as e:
            raise Exception(f"Could not read file {file_path}: {str(e)}")
    
    def validate_file_content(self, content: str, file_path: str) -> Dict[str, Any]:
        """Validate file content before encryption."""
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'stats': {}
        }
        
        try:
            # Basic content checks
            validation_result['stats']['size'] = len(content)
            validation_result['stats']['lines'] = content.count('\n') + 1
            validation_result['stats']['non_ascii_chars'] = sum(1 for c in content if ord(c) > 127)
            
            # Check for empty content
            if not content.strip():
                validation_result['errors'].append("File is empty or contains only whitespace")
                validation_result['valid'] = False
                return validation_result
            
            # Check for very large files
            if len(content) > 10 * 1024 * 1024:  # 10MB
                validation_result['warnings'].append("File is very large (>10MB), encryption may take longer")
            
            # Check for binary content
            null_bytes = content.count('\x00')
            if null_bytes > 0:
                validation_result['warnings'].append(f"File contains {null_bytes} null bytes")
            
            # Check for PHP-specific content
            if file_path.endswith('.php'):
                if not content.strip().startswith('<?php'):
                    validation_result['warnings'].append("PHP file doesn't start with <?php tag")
                
                # Check for common PHP issues
                if '<?php' in content[10:]:  # Multiple PHP opening tags
                    validation_result['warnings'].append("Multiple <?php tags detected")
            
            self.logger.debug(f"Content validation for {file_path}: {validation_result}")
            
        except Exception as e:
            validation_result['errors'].append(f"Validation error: {str(e)}")
            validation_result['valid'] = False
        
        return validation_result

    def encrypt_file_with_fallbacks(self, file_path: str, output_path: str = None,
                                   encryption_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Encrypt a file with multiple fallback mechanisms."""
        start_time = datetime.now()
        result = {
            'status': 'error',
            'file_path': file_path,
            'output_path': output_path,
            'error': None,
            'warnings': [],
            'stats': {},
            'encryption_method': None,
            'processing_time': 0
        }

        try:
            self.logger.info(f"Starting enhanced encryption for: {file_path}")

            # Step 1: Validate file exists
            if not os.path.exists(file_path):
                result['error'] = f"File not found: {file_path}"
                return result

            # Step 2: Read file content with encoding detection
            try:
                content, encoding_used = self.read_file_content(file_path)
                result['stats']['original_size'] = len(content)
                result['stats']['encoding_used'] = encoding_used
                self.logger.info(f"Read {len(content)} characters using {encoding_used} encoding")
            except Exception as e:
                result['error'] = f"Failed to read file content: {str(e)}"
                return result

            # Step 3: Validate content
            validation = self.validate_file_content(content, file_path)
            result['warnings'].extend(validation['warnings'])
            result['stats'].update(validation['stats'])

            if not validation['valid']:
                result['error'] = f"Content validation failed: {'; '.join(validation['errors'])}"
                return result

            # Step 4: Create backup
            try:
                backup_path = self.file_manager.create_backup(file_path)
                result['backup_path'] = backup_path
                self.logger.info(f"Backup created: {backup_path}")
            except Exception as e:
                result['warnings'].append(f"Backup creation failed: {str(e)}")

            # Step 5: Determine output path
            if not output_path:
                output_path = self._generate_output_path(file_path)
            result['output_path'] = output_path

            # Step 6: Set default encryption config
            if not encryption_config:
                encryption_config = self._get_default_encryption_config()

            # Step 7: Try encryption with multiple fallback levels
            encrypted_content = None
            encryption_method = None

            # Method 1: Advanced encryption
            try:
                encrypted_content = self._encrypt_with_advanced_method(content, encryption_config)
                encryption_method = 'advanced'
                self.logger.info("Successfully encrypted with advanced method")
            except Exception as e:
                self.logger.warning(f"Advanced encryption failed: {str(e)}")
                result['warnings'].append(f"Advanced encryption failed: {str(e)}")

            # Method 2: Standard encryption fallback
            if not encrypted_content:
                try:
                    encrypted_content = self._encrypt_with_standard_method(content, encryption_config)
                    encryption_method = 'standard'
                    self.logger.info("Successfully encrypted with standard method")
                except Exception as e:
                    self.logger.warning(f"Standard encryption failed: {str(e)}")
                    result['warnings'].append(f"Standard encryption failed: {str(e)}")

            # Method 3: Basic encryption fallback
            if not encrypted_content:
                try:
                    encrypted_content = self._encrypt_with_basic_method(content, encryption_config)
                    encryption_method = 'basic'
                    self.logger.info("Successfully encrypted with basic method")
                except Exception as e:
                    self.logger.warning(f"Basic encryption failed: {str(e)}")
                    result['warnings'].append(f"Basic encryption failed: {str(e)}")

            # Method 4: Emergency fallback
            if not encrypted_content:
                try:
                    encrypted_content = self._encrypt_with_emergency_fallback(content)
                    encryption_method = 'emergency'
                    self.logger.warning("Used emergency fallback encryption")
                    result['warnings'].append("Used emergency fallback encryption")
                except Exception as e:
                    result['error'] = f"All encryption methods failed. Last error: {str(e)}"
                    return result

            # Step 8: Validate encrypted content
            if not encrypted_content or len(encrypted_content) < 100:
                result['error'] = "Encrypted content is too short or empty"
                return result

            # Step 9: Write encrypted file
            try:
                # Ensure output directory exists
                output_dir = os.path.dirname(output_path)
                if output_dir:  # Only create directory if there is one
                    os.makedirs(output_dir, exist_ok=True)

                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(encrypted_content)
                self.logger.info(f"Encrypted file written to: {output_path}")
            except Exception as e:
                result['error'] = f"Failed to write encrypted file: {str(e)}"
                return result

            # Step 10: Final validation
            try:
                with open(output_path, 'r', encoding='utf-8') as f:
                    written_content = f.read()
                if len(written_content) != len(encrypted_content):
                    result['warnings'].append("Written file size differs from encrypted content")
            except Exception as e:
                result['warnings'].append(f"Could not validate written file: {str(e)}")

            # Success!
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result.update({
                'status': 'success',
                'encryption_method': encryption_method,
                'processing_time': processing_time,
                'stats': {
                    **result['stats'],
                    'encrypted_size': len(encrypted_content),
                    'size_increase_percent': ((len(encrypted_content) - len(content)) / len(content)) * 100,
                    'compression_ratio': len(content) / len(encrypted_content)
                }
            })

            self.logger.info(f"Encryption completed successfully in {processing_time:.2f}s using {encryption_method} method")

        except Exception as e:
            result['error'] = f"Unexpected error during encryption: {str(e)}"
            self.logger.error(f"Unexpected error: {str(e)}", exc_info=True)

        return result

    def _get_default_encryption_config(self) -> Dict[str, Any]:
        """Get default encryption configuration."""
        return {
            'level': 'advanced',
            'productionSafe': True,
            'loadBalancerCompatible': True,
            'antiDebugging': True,
            'integrityCheck': True,
            'domainLocking': False,
            'allowedDomains': []
        }

    def _generate_output_path(self, input_path: str) -> str:
        """Generate output path for encrypted file."""
        path_obj = Path(input_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return str(path_obj.parent / f"encrypted_{path_obj.stem}_{timestamp}{path_obj.suffix}")

    def _encrypt_with_advanced_method(self, content: str, config: Dict[str, Any]) -> str:
        """Encrypt content using advanced method with all features."""
        try:
            # Use the custom encryption engine with advanced settings
            advanced_config = {
                **config,
                'level': 'advanced',
                'multiLayer': True,
                'dynamicKeys': True,
                'antiDebugging': True,
                'integrityCheck': True
            }
            return self.custom_encryption.encrypt_content(content, advanced_config)
        except Exception as e:
            raise Exception(f"Advanced encryption failed: {str(e)}")

    def _encrypt_with_standard_method(self, content: str, config: Dict[str, Any]) -> str:
        """Encrypt content using standard method."""
        try:
            # Use the custom encryption engine with standard settings
            standard_config = {
                **config,
                'level': 'standard',
                'multiLayer': True,
                'dynamicKeys': False,
                'antiDebugging': True,
                'integrityCheck': False
            }
            return self.custom_encryption.encrypt_content(content, standard_config)
        except Exception as e:
            raise Exception(f"Standard encryption failed: {str(e)}")

    def _encrypt_with_basic_method(self, content: str, config: Dict[str, Any]) -> str:
        """Encrypt content using basic method."""
        try:
            # Use the custom encryption engine with basic settings
            basic_config = {
                **config,
                'level': 'basic',
                'multiLayer': False,
                'dynamicKeys': False,
                'antiDebugging': True,
                'integrityCheck': False
            }
            return self.custom_encryption.encrypt_content(content, basic_config)
        except Exception as e:
            raise Exception(f"Basic encryption failed: {str(e)}")

    def _encrypt_with_emergency_fallback(self, content: str) -> str:
        """Emergency fallback encryption using simple but reliable methods."""
        try:
            # Remove original <?php tag if present
            if content.startswith('<?php'):
                content = content[5:].lstrip()

            # Simple but reliable encryption
            # Layer 1: Base64 encoding
            encoded = base64.b64encode(content.encode('utf-8')).decode('ascii')

            # Layer 2: Simple XOR with random key
            key = secrets.token_hex(16)
            xor_encrypted = ""
            for i, char in enumerate(encoded):
                xor_encrypted += chr(ord(char) ^ ord(key[i % len(key)]))

            # Layer 3: Base64 again
            final_encoded = base64.b64encode(xor_encrypted.encode('latin1')).decode('ascii')

            # Create simple decoder
            decoder_template = f'''<?php
// Emergency Fallback Encrypted PHP File
// Generated: {datetime.now().isoformat()}

$encrypted_data = "{final_encoded}";
$key = "{key}";

// Decode function
function emergency_decode($data, $key) {{
    $decoded = base64_decode($data);
    $result = "";
    for ($i = 0; $i < strlen($decoded); $i++) {{
        $result .= chr(ord($decoded[$i]) ^ ord($key[$i % strlen($key)]));
    }}
    return base64_decode($result);
}}

// Execute decoded content
$original_content = emergency_decode($encrypted_data, $key);
eval($original_content);
?>'''

            return decoder_template

        except Exception as e:
            raise Exception(f"Emergency fallback encryption failed: {str(e)}")

    def batch_encrypt_files(self, file_paths: List[str], output_dir: str = None,
                           encryption_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """Encrypt multiple files in batch with progress tracking."""
        start_time = datetime.now()
        results = {
            'status': 'completed',
            'total_files': len(file_paths),
            'successful': 0,
            'failed': 0,
            'results': [],
            'processing_time': 0,
            'errors': []
        }

        self.logger.info(f"Starting batch encryption of {len(file_paths)} files")

        for i, file_path in enumerate(file_paths):
            self.logger.info(f"Processing file {i+1}/{len(file_paths)}: {file_path}")

            try:
                # Generate output path
                if output_dir:
                    filename = os.path.basename(file_path)
                    output_path = os.path.join(output_dir, f"encrypted_{filename}")
                else:
                    output_path = None

                # Encrypt the file
                result = self.encrypt_file_with_fallbacks(file_path, output_path, encryption_config)

                if result['status'] == 'success':
                    results['successful'] += 1
                    self.logger.info(f"Successfully encrypted: {file_path}")
                else:
                    results['failed'] += 1
                    self.logger.error(f"Failed to encrypt {file_path}: {result['error']}")

                results['results'].append(result)

            except Exception as e:
                results['failed'] += 1
                error_msg = f"Unexpected error processing {file_path}: {str(e)}"
                results['errors'].append(error_msg)
                self.logger.error(error_msg)

                results['results'].append({
                    'status': 'error',
                    'file_path': file_path,
                    'error': str(e)
                })

        end_time = datetime.now()
        results['processing_time'] = (end_time - start_time).total_seconds()

        self.logger.info(f"Batch encryption completed: {results['successful']} successful, {results['failed']} failed")

        return results
