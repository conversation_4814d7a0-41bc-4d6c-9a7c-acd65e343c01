2025-06-26 11:02:29,464 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_run_artisan.php
2025-06-26 11:02:29,469 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:02:29,469 - enhanced_encryption - INFO - Successfully read laravel_run_artisan.php with encoding: utf-8
2025-06-26 11:02:29,469 - enhanced_encryption - INFO - Read 8983 characters using utf-8 encoding
2025-06-26 11:02:29,469 - enhanced_encryption - DEBUG - Content validation for laravel_run_artisan.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 8983, 'lines': 276, 'non_ascii_chars': 6}}
2025-06-26 11:02:29,470 - enhanced_encryption - INFO - Backup created: backups/laravel_run_artisan_backup_20250626_110229.php
2025-06-26 11:02:29,470 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:02:52,880 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_run_artisan.php
2025-06-26 11:02:52,884 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:02:52,884 - enhanced_encryption - INFO - Successfully read laravel_run_artisan.php with encoding: utf-8
2025-06-26 11:02:52,885 - enhanced_encryption - INFO - Read 8983 characters using utf-8 encoding
2025-06-26 11:02:52,885 - enhanced_encryption - DEBUG - Content validation for laravel_run_artisan.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 8983, 'lines': 276, 'non_ascii_chars': 6}}
2025-06-26 11:02:52,885 - enhanced_encryption - INFO - Backup created: backups/laravel_run_artisan_backup_20250626_110252.php
2025-06-26 11:02:52,885 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:02:52,886 - enhanced_encryption - INFO - Encrypted file written to: test_encrypted_artisan.php
2025-06-26 11:02:52,886 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:03:08,590 - enhanced_encryption - INFO - Starting batch encryption of 8 files
2025-06-26 11:03:08,590 - enhanced_encryption - INFO - Processing file 1/8: laravel_db_migrate.php
2025-06-26 11:03:08,590 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_db_migrate.php
2025-06-26 11:03:08,612 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:03:08,613 - enhanced_encryption - INFO - Successfully read laravel_db_migrate.php with encoding: utf-8
2025-06-26 11:03:08,613 - enhanced_encryption - INFO - Read 17869 characters using utf-8 encoding
2025-06-26 11:03:08,613 - enhanced_encryption - DEBUG - Content validation for laravel_db_migrate.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 17869, 'lines': 525, 'non_ascii_chars': 6}}
2025-06-26 11:03:08,614 - enhanced_encryption - INFO - Backup created: backups/laravel_db_migrate_backup_20250626_110308.php
2025-06-26 11:03:08,614 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,614 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_db_migrate.php
2025-06-26 11:03:08,615 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:03:08,615 - enhanced_encryption - INFO - Successfully encrypted: laravel_db_migrate.php
2025-06-26 11:03:08,615 - enhanced_encryption - INFO - Processing file 2/8: laravel_db_restore.php
2025-06-26 11:03:08,615 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_db_restore.php
2025-06-26 11:03:08,637 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:03:08,637 - enhanced_encryption - INFO - Successfully read laravel_db_restore.php with encoding: utf-8
2025-06-26 11:03:08,637 - enhanced_encryption - INFO - Read 26157 characters using utf-8 encoding
2025-06-26 11:03:08,638 - enhanced_encryption - DEBUG - Content validation for laravel_db_restore.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 26157, 'lines': 694, 'non_ascii_chars': 10}}
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Backup created: backups/laravel_db_restore_backup_20250626_110308.php
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_db_restore.php
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Successfully encrypted: laravel_db_restore.php
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Processing file 3/8: laravel_developer_toolkit.php
2025-06-26 11:03:08,639 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_developer_toolkit.php
2025-06-26 11:03:08,642 - enhanced_encryption - DEBUG - Chardet detected encoding: ascii (confidence: 1.0)
2025-06-26 11:03:08,643 - enhanced_encryption - INFO - Successfully read laravel_developer_toolkit.php with encoding: ascii
2025-06-26 11:03:08,643 - enhanced_encryption - INFO - Read 37585 characters using ascii encoding
2025-06-26 11:03:08,644 - enhanced_encryption - DEBUG - Content validation for laravel_developer_toolkit.php: {'valid': True, 'warnings': ["PHP file doesn't start with <?php tag"], 'errors': [], 'stats': {'size': 37585, 'lines': 943, 'non_ascii_chars': 116}}
2025-06-26 11:03:08,645 - enhanced_encryption - INFO - Backup created: backups/laravel_developer_toolkit_backup_20250626_110308.php
2025-06-26 11:03:08,645 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,646 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_developer_toolkit.php
2025-06-26 11:03:08,646 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:03:08,646 - enhanced_encryption - INFO - Successfully encrypted: laravel_developer_toolkit.php
2025-06-26 11:03:08,646 - enhanced_encryption - INFO - Processing file 4/8: laravel_npm_build.php
2025-06-26 11:03:08,646 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_npm_build.php
2025-06-26 11:03:08,668 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:03:08,668 - enhanced_encryption - INFO - Successfully read laravel_npm_build.php with encoding: utf-8
2025-06-26 11:03:08,668 - enhanced_encryption - INFO - Read 27264 characters using utf-8 encoding
2025-06-26 11:03:08,669 - enhanced_encryption - DEBUG - Content validation for laravel_npm_build.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 27264, 'lines': 785, 'non_ascii_chars': 6}}
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Backup created: backups/laravel_npm_build_backup_20250626_110308.php
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_npm_build.php
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Successfully encrypted: laravel_npm_build.php
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Processing file 5/8: laravel_permissions_fixer.php
2025-06-26 11:03:08,670 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_permissions_fixer.php
2025-06-26 11:03:08,672 - enhanced_encryption - DEBUG - Chardet detected encoding: ascii (confidence: 1.0)
2025-06-26 11:03:08,672 - enhanced_encryption - INFO - Successfully read laravel_permissions_fixer.php with encoding: ascii
2025-06-26 11:03:08,672 - enhanced_encryption - INFO - Read 15416 characters using ascii encoding
2025-06-26 11:03:08,673 - enhanced_encryption - DEBUG - Content validation for laravel_permissions_fixer.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 15416, 'lines': 439, 'non_ascii_chars': 34}}
2025-06-26 11:03:08,673 - enhanced_encryption - INFO - Backup created: backups/laravel_permissions_fixer_backup_20250626_110308.php
2025-06-26 11:03:08,673 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,674 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_permissions_fixer.php
2025-06-26 11:03:08,674 - enhanced_encryption - INFO - Encryption completed successfully in 0.00s using advanced method
2025-06-26 11:03:08,674 - enhanced_encryption - INFO - Successfully encrypted: laravel_permissions_fixer.php
2025-06-26 11:03:08,674 - enhanced_encryption - INFO - Processing file 6/8: laravel_prod_error-fixer.php
2025-06-26 11:03:08,674 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_prod_error-fixer.php
2025-06-26 11:03:08,718 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.7525)
2025-06-26 11:03:08,718 - enhanced_encryption - INFO - Successfully read laravel_prod_error-fixer.php with encoding: utf-8
2025-06-26 11:03:08,718 - enhanced_encryption - INFO - Read 30602 characters using utf-8 encoding
2025-06-26 11:03:08,719 - enhanced_encryption - DEBUG - Content validation for laravel_prod_error-fixer.php: {'valid': True, 'warnings': ['Multiple <?php tags detected'], 'errors': [], 'stats': {'size': 30602, 'lines': 764, 'non_ascii_chars': 36}}
2025-06-26 11:03:08,720 - enhanced_encryption - INFO - Backup created: backups/laravel_prod_error-fixer_backup_20250626_110308.php
2025-06-26 11:03:08,720 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,721 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_prod_error-fixer.php
2025-06-26 11:03:08,721 - enhanced_encryption - INFO - Encryption completed successfully in 0.05s using advanced method
2025-06-26 11:03:08,721 - enhanced_encryption - INFO - Successfully encrypted: laravel_prod_error-fixer.php
2025-06-26 11:03:08,721 - enhanced_encryption - INFO - Processing file 7/8: laravel_run_artisan.php
2025-06-26 11:03:08,721 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_run_artisan.php
2025-06-26 11:03:08,725 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:03:08,725 - enhanced_encryption - INFO - Successfully read laravel_run_artisan.php with encoding: utf-8
2025-06-26 11:03:08,725 - enhanced_encryption - INFO - Read 8983 characters using utf-8 encoding
2025-06-26 11:03:08,725 - enhanced_encryption - DEBUG - Content validation for laravel_run_artisan.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 8983, 'lines': 276, 'non_ascii_chars': 6}}
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Backup created: backups/laravel_run_artisan_backup_20250626_110308.php
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_run_artisan.php
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Successfully encrypted: laravel_run_artisan.php
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Processing file 8/8: laravel_symlink_creator.php
2025-06-26 11:03:08,726 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_symlink_creator.php
2025-06-26 11:03:08,731 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:03:08,731 - enhanced_encryption - INFO - Successfully read laravel_symlink_creator.php with encoding: utf-8
2025-06-26 11:03:08,731 - enhanced_encryption - INFO - Read 48412 characters using utf-8 encoding
2025-06-26 11:03:08,733 - enhanced_encryption - DEBUG - Content validation for laravel_symlink_creator.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 48412, 'lines': 1123, 'non_ascii_chars': 46}}
2025-06-26 11:03:08,733 - enhanced_encryption - INFO - Backup created: backups/laravel_symlink_creator_backup_20250626_110308.php
2025-06-26 11:03:08,734 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:03:08,734 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110308/encrypted_laravel_symlink_creator.php
2025-06-26 11:03:08,734 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:03:08,734 - enhanced_encryption - INFO - Successfully encrypted: laravel_symlink_creator.php
2025-06-26 11:03:08,734 - enhanced_encryption - INFO - Batch encryption completed: 8 successful, 0 failed
2025-06-26 11:07:43,600 - enhanced_encryption - INFO - Starting batch encryption of 8 files
2025-06-26 11:07:43,600 - enhanced_encryption - INFO - Processing file 1/8: laravel_db_migrate.php
2025-06-26 11:07:43,600 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_db_migrate.php
2025-06-26 11:07:43,622 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:07:43,622 - enhanced_encryption - INFO - Successfully read laravel_db_migrate.php with encoding: utf-8
2025-06-26 11:07:43,622 - enhanced_encryption - INFO - Read 17869 characters using utf-8 encoding
2025-06-26 11:07:43,623 - enhanced_encryption - DEBUG - Content validation for laravel_db_migrate.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 17869, 'lines': 525, 'non_ascii_chars': 6}}
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Backup created: backups/laravel_db_migrate_backup_20250626_110743.php
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_db_migrate.php
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Successfully encrypted: laravel_db_migrate.php
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Processing file 2/8: laravel_db_restore.php
2025-06-26 11:07:43,624 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_db_restore.php
2025-06-26 11:07:43,646 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:07:43,646 - enhanced_encryption - INFO - Successfully read laravel_db_restore.php with encoding: utf-8
2025-06-26 11:07:43,646 - enhanced_encryption - INFO - Read 26157 characters using utf-8 encoding
2025-06-26 11:07:43,647 - enhanced_encryption - DEBUG - Content validation for laravel_db_restore.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 26157, 'lines': 694, 'non_ascii_chars': 10}}
2025-06-26 11:07:43,647 - enhanced_encryption - INFO - Backup created: backups/laravel_db_restore_backup_20250626_110743.php
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_db_restore.php
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Successfully encrypted: laravel_db_restore.php
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Processing file 3/8: laravel_developer_toolkit.php
2025-06-26 11:07:43,648 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_developer_toolkit.php
2025-06-26 11:07:43,651 - enhanced_encryption - DEBUG - Chardet detected encoding: ascii (confidence: 1.0)
2025-06-26 11:07:43,651 - enhanced_encryption - INFO - Successfully read laravel_developer_toolkit.php with encoding: ascii
2025-06-26 11:07:43,651 - enhanced_encryption - INFO - Read 37585 characters using ascii encoding
2025-06-26 11:07:43,652 - enhanced_encryption - DEBUG - Content validation for laravel_developer_toolkit.php: {'valid': True, 'warnings': ["PHP file doesn't start with <?php tag"], 'errors': [], 'stats': {'size': 37585, 'lines': 943, 'non_ascii_chars': 116}}
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Backup created: backups/laravel_developer_toolkit_backup_20250626_110743.php
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_developer_toolkit.php
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Successfully encrypted: laravel_developer_toolkit.php
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Processing file 4/8: laravel_npm_build.php
2025-06-26 11:07:43,653 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_npm_build.php
2025-06-26 11:07:43,676 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.938125)
2025-06-26 11:07:43,676 - enhanced_encryption - INFO - Successfully read laravel_npm_build.php with encoding: utf-8
2025-06-26 11:07:43,676 - enhanced_encryption - INFO - Read 27264 characters using utf-8 encoding
2025-06-26 11:07:43,677 - enhanced_encryption - DEBUG - Content validation for laravel_npm_build.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 27264, 'lines': 785, 'non_ascii_chars': 6}}
2025-06-26 11:07:43,677 - enhanced_encryption - INFO - Backup created: backups/laravel_npm_build_backup_20250626_110743.php
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_npm_build.php
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Encryption completed successfully in 0.02s using advanced method
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Successfully encrypted: laravel_npm_build.php
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Processing file 5/8: laravel_permissions_fixer.php
2025-06-26 11:07:43,678 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_permissions_fixer.php
2025-06-26 11:07:43,680 - enhanced_encryption - DEBUG - Chardet detected encoding: ascii (confidence: 1.0)
2025-06-26 11:07:43,680 - enhanced_encryption - INFO - Successfully read laravel_permissions_fixer.php with encoding: ascii
2025-06-26 11:07:43,680 - enhanced_encryption - INFO - Read 15416 characters using ascii encoding
2025-06-26 11:07:43,681 - enhanced_encryption - DEBUG - Content validation for laravel_permissions_fixer.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 15416, 'lines': 439, 'non_ascii_chars': 34}}
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Backup created: backups/laravel_permissions_fixer_backup_20250626_110743.php
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_permissions_fixer.php
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Encryption completed successfully in 0.00s using advanced method
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Successfully encrypted: laravel_permissions_fixer.php
2025-06-26 11:07:43,681 - enhanced_encryption - INFO - Processing file 6/8: laravel_prod_error-fixer.php
2025-06-26 11:07:43,682 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_prod_error-fixer.php
2025-06-26 11:07:43,726 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.7525)
2025-06-26 11:07:43,726 - enhanced_encryption - INFO - Successfully read laravel_prod_error-fixer.php with encoding: utf-8
2025-06-26 11:07:43,726 - enhanced_encryption - INFO - Read 30602 characters using utf-8 encoding
2025-06-26 11:07:43,727 - enhanced_encryption - DEBUG - Content validation for laravel_prod_error-fixer.php: {'valid': True, 'warnings': ['Multiple <?php tags detected'], 'errors': [], 'stats': {'size': 30602, 'lines': 764, 'non_ascii_chars': 36}}
2025-06-26 11:07:43,728 - enhanced_encryption - INFO - Backup created: backups/laravel_prod_error-fixer_backup_20250626_110743.php
2025-06-26 11:07:43,728 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,728 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_prod_error-fixer.php
2025-06-26 11:07:43,729 - enhanced_encryption - INFO - Encryption completed successfully in 0.05s using advanced method
2025-06-26 11:07:43,729 - enhanced_encryption - INFO - Successfully encrypted: laravel_prod_error-fixer.php
2025-06-26 11:07:43,729 - enhanced_encryption - INFO - Processing file 7/8: laravel_run_artisan.php
2025-06-26 11:07:43,729 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_run_artisan.php
2025-06-26 11:07:43,733 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:07:43,733 - enhanced_encryption - INFO - Successfully read laravel_run_artisan.php with encoding: utf-8
2025-06-26 11:07:43,733 - enhanced_encryption - INFO - Read 8983 characters using utf-8 encoding
2025-06-26 11:07:43,733 - enhanced_encryption - DEBUG - Content validation for laravel_run_artisan.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 8983, 'lines': 276, 'non_ascii_chars': 6}}
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Backup created: backups/laravel_run_artisan_backup_20250626_110743.php
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_run_artisan.php
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Successfully encrypted: laravel_run_artisan.php
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Processing file 8/8: laravel_symlink_creator.php
2025-06-26 11:07:43,734 - enhanced_encryption - INFO - Starting enhanced encryption for: laravel_symlink_creator.php
2025-06-26 11:07:43,739 - enhanced_encryption - DEBUG - Chardet detected encoding: utf-8 (confidence: 0.99)
2025-06-26 11:07:43,739 - enhanced_encryption - INFO - Successfully read laravel_symlink_creator.php with encoding: utf-8
2025-06-26 11:07:43,739 - enhanced_encryption - INFO - Read 48412 characters using utf-8 encoding
2025-06-26 11:07:43,741 - enhanced_encryption - DEBUG - Content validation for laravel_symlink_creator.php: {'valid': True, 'warnings': [], 'errors': [], 'stats': {'size': 48412, 'lines': 1123, 'non_ascii_chars': 46}}
2025-06-26 11:07:43,741 - enhanced_encryption - INFO - Backup created: backups/laravel_symlink_creator_backup_20250626_110743.php
2025-06-26 11:07:43,742 - enhanced_encryption - INFO - Successfully encrypted with advanced method
2025-06-26 11:07:43,742 - enhanced_encryption - INFO - Encrypted file written to: encrypted_laravel_files_20250626_110743/encrypted_laravel_symlink_creator.php
2025-06-26 11:07:43,742 - enhanced_encryption - INFO - Encryption completed successfully in 0.01s using advanced method
2025-06-26 11:07:43,742 - enhanced_encryption - INFO - Successfully encrypted: laravel_symlink_creator.php
2025-06-26 11:07:43,742 - enhanced_encryption - INFO - Batch encryption completed: 8 successful, 0 failed
